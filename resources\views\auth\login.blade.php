<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - sub4short+</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <style>
        body { background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%); }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-blue-800 to-blue-600 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-all duration-300">
    <div class="bg-white/90 dark:bg-gray-900/90 rounded-2xl shadow-2xl p-8 w-full max-w-md backdrop-blur-md">
        <div class="flex flex-col items-center mb-6">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full flex items-center justify-center mb-2 shadow-lg">
                <i class="fa-solid fa-link text-white text-3xl"></i>
            </div>
            <h1 class="text-2xl font-bold mb-2 text-gray-900 dark:text-white">Masuk ke <span class="text-blue-600">sub4short+</span></h1>
            <p class="text-gray-500 dark:text-gray-300 text-sm">Silakan login untuk melanjutkan</p>
        </div>
        <form action="{{ route('login.attempt') }}" method="POST" class="flex flex-col gap-5 mt-2">
            @csrf
            <div>
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Email</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-600">
                        <i class="fa-regular fa-user"></i>
                    </span>
                    <input type="email" name="email" required class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="<EMAIL>" value="{{ old('email') }}">
                </div>
            </div>
            <div x-data="{ show: false }">
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Password</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-600">
                        <i class="fa-solid fa-lock"></i>
                    </span>
                    <input :type="show ? 'text' : 'password'" name="password" required class="w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="Password">
                    <button type="button" @click="show = !show" class="absolute right-3 top-1/2 -translate-y-1/2 focus:outline-none">
                        <template x-if="show">
                            <i class="fa-regular fa-eye-slash text-blue-600"></i>
                        </template>
                        <template x-if="!show">
                            <i class="fa-regular fa-eye text-blue-600"></i>
                        </template>
                    </button>
                </div>
            </div>
            <div class="flex justify-between items-center text-sm mt-1">
                <label class="inline-flex items-center gap-2 text-gray-600 dark:text-gray-400">
                    <input type="checkbox" name="remember" class="rounded border-gray-300 dark:border-gray-600">
                    Ingat saya
                </label>
                <a href="{{ url('/auth/forgot-password') }}" class="text-blue-600 hover:underline">Lupa password?</a>
            </div>
            @if($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded text-sm flex items-center gap-2 animate-pulse">
                    <i class="fa-solid fa-circle-exclamation"></i>
                    {{ $errors->first() }}
                </div>
            @endif
            <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-700 text-white py-2 rounded font-semibold shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95 flex items-center justify-center gap-2">
                <i class="fa-solid fa-arrow-right-to-bracket"></i>
                Masuk
            </button>
        </form>
        <div class="mt-6 text-center text-sm text-gray-600 dark:text-gray-300">
            Belum punya akun? <a href="{{ url('/auth/register') }}" class="text-blue-600 hover:underline font-semibold">Daftar</a>
        </div>
    </div>
</body>
</html> 