<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;

class QuickActionsWidget extends Widget
{
    protected static string $view = 'filament.widgets.quick-actions';
    
    protected static ?int $sort = 7;
    
    protected int | string | array $columnSpan = 'full';

    protected function getViewData(): array
    {
        return [
            'actions' => [
                [
                    'title' => 'Create New Shortlink',
                    'description' => 'Generate a new short URL',
                    'icon' => 'heroicon-o-plus-circle',
                    'color' => 'primary',
                    'url' => '#',
                ],
                [
                    'title' => 'View Analytics',
                    'description' => 'Check detailed statistics',
                    'icon' => 'heroicon-o-chart-bar',
                    'color' => 'success',
                    'url' => '#',
                ],
                [
                    'title' => 'Manage Users',
                    'description' => 'User management panel',
                    'icon' => 'heroicon-o-users',
                    'color' => 'warning',
                    'url' => '#',
                ],
                [
                    'title' => 'Export Data',
                    'description' => 'Download reports and data',
                    'icon' => 'heroicon-o-arrow-down-tray',
                    'color' => 'info',
                    'url' => '#',
                ],
                [
                    'title' => 'Settings',
                    'description' => 'Configure application settings',
                    'icon' => 'heroicon-o-cog-6-tooth',
                    'color' => 'gray',
                    'url' => '#',
                ],
                [
                    'title' => 'Support',
                    'description' => 'Get help and support',
                    'icon' => 'heroicon-o-question-mark-circle',
                    'color' => 'danger',
                    'url' => '#',
                ],
            ]
        ];
    }
}
