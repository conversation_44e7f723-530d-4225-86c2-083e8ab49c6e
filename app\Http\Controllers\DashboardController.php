<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Link;
use App\Models\Click;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function getDashboardStats()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        \Log::info('Dashboard stats requested for user: ' . $user->id);

        // Get dashboard statistics
        $stats = [
            'totalLinks' => $user->links()->count(),
            'totalClicks' => $user->links()->sum('clicks'),
            'todayClicks' => $user->clicks()->whereDate('created_at', today())->count(),
            'totalEarnings' => (float) $user->links()->sum('earnings'),
        ];

        // Get link limit information
        $linkLimit = [
            'current' => $user->links_used_this_month,
            'max' => $user->monthly_link_limit,
            'resetDate' => Carbon::now()->endOfMonth()->format('d M Y'),
            'plan' => 'Bronze'
        ];

        // Get recent notifications
        $notifications = $this->getRecentNotifications($user);

        return response()->json([
            'stats' => $stats,
            'linkLimit' => $linkLimit,
            'notifications' => $notifications
        ])->header('Content-Type', 'application/json');
    }

    public function getRecentLinks()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $links = $user->links()
                     ->with('clicks')
                     ->orderBy('created_at', 'desc')
                     ->limit(10)
                     ->get()
                     ->map(function ($link) {
                         return [
                             'id' => $link->id,
                             'original_url' => $link->original_url,
                             'short_code' => $link->short_code,
                             'short_url' => $link->short_url,
                             'title' => $link->title,
                             'clicks' => $link->clicks,
                             'today_clicks' => $link->clicks()->whereDate('created_at', today())->count(),
                             'earnings' => (float) $link->earnings,
                             'created_at' => $link->created_at->format('d M Y H:i'),
                             'last_clicked_at' => $link->last_clicked_at ? $link->last_clicked_at->format('d M Y H:i') : null,
                             'is_active' => $link->is_active
                         ];
                     });

        return response()->json(['links' => $links])->header('Content-Type', 'application/json');
    }

    public function getAnalytics(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $period = $request->get('period', '7days'); // 7days, 30days, 90days

        $startDate = match($period) {
            '7days' => Carbon::now()->subDays(7),
            '30days' => Carbon::now()->subDays(30),
            '90days' => Carbon::now()->subDays(90),
            default => Carbon::now()->subDays(7)
        };

        // Get clicks data for chart
        $clicksData = $user->clicks()
                          ->where('created_at', '>=', $startDate)
                          ->selectRaw('DATE(created_at) as date, COUNT(*) as clicks, SUM(earnings) as earnings')
                          ->groupBy('date')
                          ->orderBy('date')
                          ->get();

        // Get top performing links
        $topLinks = $user->links()
                        ->where('created_at', '>=', $startDate)
                        ->orderBy('clicks', 'desc')
                        ->limit(5)
                        ->get(['id', 'title', 'short_code', 'clicks', 'earnings']);

        // Get device statistics
        $deviceStats = $user->clicks()
                           ->where('created_at', '>=', $startDate)
                           ->selectRaw('device_type, COUNT(*) as count')
                           ->groupBy('device_type')
                           ->get();

        // Get country statistics
        $countryStats = $user->clicks()
                            ->where('created_at', '>=', $startDate)
                            ->selectRaw('country, COUNT(*) as count')
                            ->groupBy('country')
                            ->orderBy('count', 'desc')
                            ->limit(5)
                            ->get();

        return response()->json([
            'period' => $period,
            'clicksData' => $clicksData,
            'topLinks' => $topLinks,
            'deviceStats' => $deviceStats,
            'countryStats' => $countryStats,
            'totalClicks' => $clicksData->sum('clicks'),
            'totalEarnings' => (float) $clicksData->sum('earnings')
        ]);
    }

    private function getRecentNotifications($user)
    {
        $notifications = [];

        // Check if user is close to link limit
        $remaining = $user->remaining_links;
        if ($remaining <= 10) {
            $notifications[] = [
                'id' => 'limit_warning',
                'icon' => 'fa-exclamation-triangle',
                'iconColor' => 'text-red-600 dark:text-red-400',
                'iconBg' => 'bg-red-100 dark:bg-red-900/30',
                'title' => 'Limit link hampir habis',
                'message' => "Tersisa {$remaining} dari {$user->monthly_link_limit} link bulan ini",
                'time' => 'Baru saja',
                'unread' => true
            ];
        }

        // Recent earnings notification
        $todayEarnings = $user->clicks()->whereDate('created_at', today())->sum('earnings');
        if ($todayEarnings > 0) {
            $notifications[] = [
                'id' => 'earnings_today',
                'icon' => 'fa-coins',
                'iconColor' => 'text-yellow-600 dark:text-yellow-400',
                'iconBg' => 'bg-yellow-100 dark:bg-yellow-900/30',
                'title' => 'Pendapatan hari ini: Rp ' . number_format($todayEarnings, 0, ',', '.'),
                'message' => 'Total klik: ' . $user->clicks()->whereDate('created_at', today())->count(),
                'time' => '1 jam yang lalu',
                'unread' => true
            ];
        }

        // Recent link creation
        $recentLink = $user->links()->latest()->first();
        if ($recentLink && $recentLink->created_at->isToday()) {
            $notifications[] = [
                'id' => 'recent_link',
                'icon' => 'fa-link',
                'iconColor' => 'text-green-600 dark:text-green-400',
                'iconBg' => 'bg-green-100 dark:bg-green-900/30',
                'title' => 'Shortlink berhasil dibuat',
                'message' => 'Link: ' . $recentLink->short_url,
                'time' => $recentLink->created_at->diffForHumans(),
                'unread' => true
            ];
        }

        return $notifications;
    }

    public function createLink(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Validate request
        $request->validate([
            'url' => 'required|url|max:2048',
            'alias' => 'nullable|string|max:100|alpha_dash|unique:links,custom_alias'
        ]);

        // Check link limit
        if ($user->links_used_this_month >= $user->monthly_link_limit) {
            return response()->json([
                'error' => 'Link limit exceeded',
                'message' => 'Anda telah mencapai batas maksimal link untuk bulan ini'
            ], 400);
        }

        try {
            // Create new link
            $link = new Link();
            $link->user_id = $user->id;
            $link->original_url = $request->url;
            $link->custom_alias = $request->alias;
            $link->short_code = $request->alias ?: Link::generateUniqueShortCode();

            // Get page title if possible
            $link->title = $this->getPageTitle($request->url);

            $link->save();

            return response()->json([
                'success' => true,
                'message' => 'Shortlink berhasil dibuat',
                'link' => [
                    'id' => $link->id,
                    'original_url' => $link->original_url,
                    'short_code' => $link->short_code,
                    'short_url' => $link->short_url,
                    'title' => $link->title,
                    'clicks' => $link->clicks,
                    'earnings' => (float) $link->earnings,
                    'created_at' => $link->created_at->format('d M Y H:i')
                ]
            ])->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            \Log::error('Error creating link: ' . $e->getMessage());
            return response()->json([
                'error' => 'Creation failed',
                'message' => 'Gagal membuat shortlink: ' . $e->getMessage()
            ], 500)->header('Content-Type', 'application/json');
        }
    }

    private function getPageTitle($url)
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'Mozilla/5.0 (compatible; Sub4Short Bot)'
                ]
            ]);

            $html = @file_get_contents($url, false, $context);
            if ($html) {
                preg_match('/<title[^>]*>(.*?)<\/title>/is', $html, $matches);
                if (isset($matches[1])) {
                    return trim(html_entity_decode($matches[1]));
                }
            }
        } catch (\Exception $e) {
            // Ignore errors, just return null
        }

        return null;
    }

    public function getRecentActivity()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        \Log::info('Getting recent activity for user: ' . $user->id);
        $activities = [];

        // Get recent links created (last 7 days)
        $recentLinks = $user->links()
                           ->where('created_at', '>=', Carbon::now()->subDays(7))
                           ->orderBy('created_at', 'desc')
                           ->limit(5)
                           ->get();

        foreach ($recentLinks as $link) {
            $activities[] = [
                'id' => 'link_' . $link->id,
                'type' => 'link_created',
                'icon' => 'fa-link',
                'iconColor' => 'text-blue-600 dark:text-blue-400',
                'iconBg' => 'bg-blue-100 dark:bg-blue-900/30',
                'title' => 'Shortlink baru dibuat',
                'description' => $link->short_url,
                'timestamp' => $link->created_at,
                'time_ago' => $link->created_at->diffForHumans(),
                'data' => [
                    'link_id' => $link->id,
                    'short_code' => $link->short_code,
                    'title' => $link->title
                ]
            ];
        }

        // Get recent clicks (last 24 hours, grouped by hour)
        $recentClicks = $user->clicks()
                            ->where('created_at', '>=', Carbon::now()->subDay())
                            ->selectRaw('COUNT(*) as click_count, SUM(earnings) as total_earnings, MAX(created_at) as latest_click, country')
                            ->groupBy('country')
                            ->orderBy('latest_click', 'desc')
                            ->limit(3)
                            ->get();

        foreach ($recentClicks as $clickGroup) {
            $activities[] = [
                'id' => 'clicks_' . $clickGroup->country . '_' . $clickGroup->latest_click->timestamp,
                'type' => 'clicks_received',
                'icon' => 'fa-mouse-pointer',
                'iconColor' => 'text-green-600 dark:text-green-400',
                'iconBg' => 'bg-green-100 dark:bg-green-900/30',
                'title' => $clickGroup->click_count . ' klik baru',
                'description' => 'Dari ' . $clickGroup->country,
                'timestamp' => Carbon::parse($clickGroup->latest_click),
                'time_ago' => Carbon::parse($clickGroup->latest_click)->diffForHumans(),
                'data' => [
                    'click_count' => $clickGroup->click_count,
                    'country' => $clickGroup->country,
                    'earnings' => (float) $clickGroup->total_earnings
                ]
            ];
        }

        // Get recent earnings (last 24 hours, significant amounts)
        $recentEarnings = $user->clicks()
                              ->where('created_at', '>=', Carbon::now()->subDay())
                              ->where('earnings', '>', 0.10) // Only show earnings > Rp 0.10
                              ->selectRaw('SUM(earnings) as total_earnings, COUNT(*) as click_count, MAX(created_at) as latest_earning')
                              ->groupBy(DB::raw('HOUR(created_at)'))
                              ->orderBy('latest_earning', 'desc')
                              ->limit(3)
                              ->get();

        foreach ($recentEarnings as $earning) {
            if ($earning->total_earnings > 0) {
                $activities[] = [
                    'id' => 'earnings_' . $earning->latest_earning,
                    'type' => 'earnings_received',
                    'icon' => 'fa-money-bill-wave',
                    'iconColor' => 'text-purple-600 dark:text-purple-400',
                    'iconBg' => 'bg-purple-100 dark:bg-purple-900/30',
                    'title' => 'Pendapatan baru',
                    'description' => '+Rp ' . number_format($earning->total_earnings, 0, ',', '.'),
                    'timestamp' => Carbon::parse($earning->latest_earning),
                    'time_ago' => Carbon::parse($earning->latest_earning)->diffForHumans(),
                    'data' => [
                        'amount' => (float) $earning->total_earnings,
                        'click_count' => $earning->click_count
                    ]
                ];
            }
        }

        // Sort all activities by timestamp
        usort($activities, function($a, $b) {
            return $b['timestamp']->timestamp - $a['timestamp']->timestamp;
        });

        // Limit to 10 most recent activities
        $activities = array_slice($activities, 0, 10);

        // Remove timestamp object for JSON response
        foreach ($activities as &$activity) {
            unset($activity['timestamp']);
        }

        \Log::info('Activities found: ' . count($activities));

        // If no activities, add some sample activities for demo
        if (empty($activities)) {
            $activities = [
                [
                    'id' => 'demo_1',
                    'type' => 'link_created',
                    'icon' => 'fa-link',
                    'iconColor' => 'text-blue-600 dark:text-blue-400',
                    'iconBg' => 'bg-blue-100 dark:bg-blue-900/30',
                    'title' => 'Shortlink baru dibuat',
                    'description' => 'sub4short.plus/s4s-abc/1234',
                    'time_ago' => '2 menit yang lalu',
                    'data' => []
                ],
                [
                    'id' => 'demo_2',
                    'type' => 'clicks_received',
                    'icon' => 'fa-mouse-pointer',
                    'iconColor' => 'text-green-600 dark:text-green-400',
                    'iconBg' => 'bg-green-100 dark:bg-green-900/30',
                    'title' => '5 klik baru',
                    'description' => 'Dari Indonesia',
                    'time_ago' => '15 menit yang lalu',
                    'data' => []
                ],
                [
                    'id' => 'demo_3',
                    'type' => 'earnings_received',
                    'icon' => 'fa-money-bill-wave',
                    'iconColor' => 'text-purple-600 dark:text-purple-400',
                    'iconBg' => 'bg-purple-100 dark:bg-purple-900/30',
                    'title' => 'Pendapatan baru',
                    'description' => '+Rp 2.500',
                    'time_ago' => '1 jam yang lalu',
                    'data' => []
                ]
            ];
        }

        return response()->json([
            'activities' => $activities
        ])->header('Content-Type', 'application/json');
    }

    public function getLinks(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $perPage = $request->get('per_page', 10);
        $search = $request->get('search');
        $status = $request->get('status');

        $query = $user->links()->with('clicks');

        // Search functionality
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('original_url', 'like', "%{$search}%")
                  ->orWhere('short_code', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($status !== null) {
            $query->where('is_active', $status === 'active');
        }

        $links = $query->orderBy('created_at', 'desc')
                      ->paginate($perPage);

        $formattedLinks = $links->getCollection()->map(function ($link) {
            return [
                'id' => $link->id,
                'original_url' => $link->original_url,
                'short_code' => $link->short_code,
                'short_url' => $link->short_url,
                'title' => $link->title,
                'description' => $link->description,
                'clicks' => $link->clicks,
                'today_clicks' => $link->clicks()->whereDate('created_at', today())->count(),
                'earnings' => (float) $link->earnings,
                'is_active' => $link->is_active,
                'created_at' => $link->created_at->format('d M Y H:i'),
                'created_at_human' => $link->created_at->diffForHumans(),
                'last_clicked_at' => $link->last_clicked_at ? $link->last_clicked_at->format('d M Y H:i') : null,
            ];
        });

        return response()->json([
            'links' => $formattedLinks,
            'pagination' => [
                'current_page' => $links->currentPage(),
                'last_page' => $links->lastPage(),
                'per_page' => $links->perPage(),
                'total' => $links->total(),
                'from' => $links->firstItem(),
                'to' => $links->lastItem(),
            ]
        ]);
    }

    public function updateLink(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $link = $user->links()->findOrFail($id);

        $request->validate([
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean'
        ]);

        $link->update($request->only(['title', 'description', 'is_active']));

        return response()->json([
            'success' => true,
            'message' => 'Link berhasil diperbarui',
            'link' => [
                'id' => $link->id,
                'original_url' => $link->original_url,
                'short_code' => $link->short_code,
                'short_url' => $link->short_url,
                'title' => $link->title,
                'description' => $link->description,
                'clicks' => $link->clicks,
                'earnings' => (float) $link->earnings,
                'is_active' => $link->is_active,
                'created_at' => $link->created_at->format('d M Y H:i')
            ]
        ]);
    }

    public function deleteLink($id)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $link = $user->links()->findOrFail($id);
        $link->delete();

        return response()->json([
            'success' => true,
            'message' => 'Link berhasil dihapus'
        ]);
    }

    public function toggleLinkStatus($id)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $link = $user->links()->findOrFail($id);
        $link->is_active = !$link->is_active;
        $link->save();

        return response()->json([
            'success' => true,
            'message' => 'Status link berhasil diubah',
            'is_active' => $link->is_active
        ]);
    }
}
