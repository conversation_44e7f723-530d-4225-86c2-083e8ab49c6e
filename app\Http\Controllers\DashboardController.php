<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Link;
use App\Models\Click;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function getDashboardStats()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Get dashboard statistics
        $stats = [
            'totalLinks' => $user->links()->count(),
            'totalClicks' => $user->links()->sum('clicks'),
            'todayClicks' => $user->clicks()->whereDate('created_at', today())->count(),
            'totalEarnings' => (float) $user->links()->sum('earnings'),
        ];

        // Get link limit information
        $linkLimit = [
            'current' => $user->links_used_this_month,
            'max' => $user->monthly_link_limit,
            'resetDate' => Carbon::now()->endOfMonth()->format('d M Y'),
            'plan' => 'Bronze'
        ];

        // Get recent notifications
        $notifications = $this->getRecentNotifications($user);

        return response()->json([
            'stats' => $stats,
            'linkLimit' => $linkLimit,
            'notifications' => $notifications
        ]);
    }

    public function getRecentLinks()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $links = $user->links()
                     ->with('clicks')
                     ->orderBy('created_at', 'desc')
                     ->limit(10)
                     ->get()
                     ->map(function ($link) {
                         return [
                             'id' => $link->id,
                             'original_url' => $link->original_url,
                             'short_code' => $link->short_code,
                             'short_url' => $link->short_url,
                             'title' => $link->title,
                             'clicks' => $link->clicks,
                             'today_clicks' => $link->clicks()->whereDate('created_at', today())->count(),
                             'earnings' => (float) $link->earnings,
                             'created_at' => $link->created_at->format('d M Y H:i'),
                             'last_clicked_at' => $link->last_clicked_at ? $link->last_clicked_at->format('d M Y H:i') : null,
                             'is_active' => $link->is_active
                         ];
                     });

        return response()->json(['links' => $links]);
    }

    public function getAnalytics(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $period = $request->get('period', '7days'); // 7days, 30days, 90days

        $startDate = match($period) {
            '7days' => Carbon::now()->subDays(7),
            '30days' => Carbon::now()->subDays(30),
            '90days' => Carbon::now()->subDays(90),
            default => Carbon::now()->subDays(7)
        };

        // Get clicks data for chart
        $clicksData = $user->clicks()
                          ->where('created_at', '>=', $startDate)
                          ->selectRaw('DATE(created_at) as date, COUNT(*) as clicks, SUM(earnings) as earnings')
                          ->groupBy('date')
                          ->orderBy('date')
                          ->get();

        // Get top performing links
        $topLinks = $user->links()
                        ->where('created_at', '>=', $startDate)
                        ->orderBy('clicks', 'desc')
                        ->limit(5)
                        ->get(['id', 'title', 'short_code', 'clicks', 'earnings']);

        // Get device statistics
        $deviceStats = $user->clicks()
                           ->where('created_at', '>=', $startDate)
                           ->selectRaw('device_type, COUNT(*) as count')
                           ->groupBy('device_type')
                           ->get();

        // Get country statistics
        $countryStats = $user->clicks()
                            ->where('created_at', '>=', $startDate)
                            ->selectRaw('country, COUNT(*) as count')
                            ->groupBy('country')
                            ->orderBy('count', 'desc')
                            ->limit(5)
                            ->get();

        return response()->json([
            'period' => $period,
            'clicksData' => $clicksData,
            'topLinks' => $topLinks,
            'deviceStats' => $deviceStats,
            'countryStats' => $countryStats,
            'totalClicks' => $clicksData->sum('clicks'),
            'totalEarnings' => (float) $clicksData->sum('earnings')
        ]);
    }

    private function getRecentNotifications($user)
    {
        $notifications = [];

        // Check if user is close to link limit
        $remaining = $user->remaining_links;
        if ($remaining <= 10) {
            $notifications[] = [
                'id' => 'limit_warning',
                'icon' => 'fa-exclamation-triangle',
                'iconColor' => 'text-red-600 dark:text-red-400',
                'iconBg' => 'bg-red-100 dark:bg-red-900/30',
                'title' => 'Limit link hampir habis',
                'message' => "Tersisa {$remaining} dari {$user->monthly_link_limit} link bulan ini",
                'time' => 'Baru saja',
                'unread' => true
            ];
        }

        // Recent earnings notification
        $todayEarnings = $user->clicks()->whereDate('created_at', today())->sum('earnings');
        if ($todayEarnings > 0) {
            $notifications[] = [
                'id' => 'earnings_today',
                'icon' => 'fa-coins',
                'iconColor' => 'text-yellow-600 dark:text-yellow-400',
                'iconBg' => 'bg-yellow-100 dark:bg-yellow-900/30',
                'title' => 'Pendapatan hari ini: Rp ' . number_format($todayEarnings, 0, ',', '.'),
                'message' => 'Total klik: ' . $user->clicks()->whereDate('created_at', today())->count(),
                'time' => '1 jam yang lalu',
                'unread' => true
            ];
        }

        // Recent link creation
        $recentLink = $user->links()->latest()->first();
        if ($recentLink && $recentLink->created_at->isToday()) {
            $notifications[] = [
                'id' => 'recent_link',
                'icon' => 'fa-link',
                'iconColor' => 'text-green-600 dark:text-green-400',
                'iconBg' => 'bg-green-100 dark:bg-green-900/30',
                'title' => 'Shortlink berhasil dibuat',
                'message' => 'Link: ' . $recentLink->short_url,
                'time' => $recentLink->created_at->diffForHumans(),
                'unread' => true
            ];
        }

        return $notifications;
    }

    public function createLink(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Validate request
        $request->validate([
            'url' => 'required|url|max:2048',
            'alias' => 'nullable|string|max:100|alpha_dash|unique:links,custom_alias'
        ]);

        // Check link limit
        if ($user->links_used_this_month >= $user->monthly_link_limit) {
            return response()->json([
                'error' => 'Link limit exceeded',
                'message' => 'Anda telah mencapai batas maksimal link untuk bulan ini'
            ], 400);
        }

        try {
            // Create new link
            $link = new Link();
            $link->user_id = $user->id;
            $link->original_url = $request->url;
            $link->custom_alias = $request->alias;
            $link->short_code = $request->alias ?: Link::generateUniqueShortCode();

            // Get page title if possible
            $link->title = $this->getPageTitle($request->url);

            $link->save();

            return response()->json([
                'success' => true,
                'message' => 'Shortlink berhasil dibuat',
                'link' => [
                    'id' => $link->id,
                    'original_url' => $link->original_url,
                    'short_code' => $link->short_code,
                    'short_url' => $link->short_url,
                    'title' => $link->title,
                    'clicks' => $link->clicks,
                    'earnings' => (float) $link->earnings,
                    'created_at' => $link->created_at->format('d M Y H:i')
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Creation failed',
                'message' => 'Gagal membuat shortlink: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getPageTitle($url)
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'user_agent' => 'Mozilla/5.0 (compatible; Sub4Short Bot)'
                ]
            ]);

            $html = @file_get_contents($url, false, $context);
            if ($html) {
                preg_match('/<title[^>]*>(.*?)<\/title>/is', $html, $matches);
                if (isset($matches[1])) {
                    return trim(html_entity_decode($matches[1]));
                }
            }
        } catch (\Exception $e) {
            // Ignore errors, just return null
        }

        return null;
    }
}
