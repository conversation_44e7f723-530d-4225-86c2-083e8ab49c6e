<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Carbon\Carbon;

class InvoiceController extends Controller
{
    public function show($invoice_id)
    {
        // Sample invoice data - in real app, this would come from database
        $invoice = $this->getInvoiceData($invoice_id);

        if (!$invoice) {
            abort(404, 'Invoice not found');
        }

        return view('invoice.show', compact('invoice'));
    }

    private function getInvoiceData($invoice_id)
    {
        // Sample data - replace with actual database query
        $invoices = [
            'inv-001' => [
                'id' => 'inv-001',
                'number' => 'INV-2024-001',
                'date' => '2024-01-15',
                'due_date' => '2024-02-15',
                'status' => 'paid',
                'customer' => [
                    'name' => 'John <PERSON>',
                    'email' => '<EMAIL>',
                    'address' => 'Jl. Sudirman No. 123',
                    'city' => 'Jakarta',
                    'postal_code' => '12345',
                    'country' => 'Indonesia'
                ],
                'company' => [
                    'name' => 'Sub4Short+',
                    'address' => 'Jl. Teknologi No. 456',
                    'city' => 'Jakarta',
                    'postal_code' => '54321',
                    'country' => 'Indonesia',
                    'phone' => '+62 21 1234 5678',
                    'email' => '<EMAIL>',
                    'website' => 'https://sub4short.com'
                ],
                'items' => [
                    [
                        'description' => 'Silver Plan Subscription',
                        'period' => 'January 2024',
                        'quantity' => 1,
                        'unit_price' => 100000,
                        'total' => 100000
                    ]
                ],
                'subtotal' => 100000,
                'tax_rate' => 11,
                'tax_amount' => 11000,
                'total' => 111000,
                'payment_method' => 'Credit Card',
                'payment_date' => '2024-01-15',
                'notes' => 'Terima kasih atas pembayaran Anda. Langganan Silver Plan telah aktif.'
            ],
            'inv-002' => [
                'id' => 'inv-002',
                'number' => 'INV-2024-002',
                'date' => '2024-02-15',
                'due_date' => '2024-03-15',
                'status' => 'pending',
                'customer' => [
                    'name' => 'Jane Smith',
                    'email' => '<EMAIL>',
                    'address' => 'Jl. Gatot Subroto No. 789',
                    'city' => 'Bandung',
                    'postal_code' => '67890',
                    'country' => 'Indonesia'
                ],
                'company' => [
                    'name' => 'Sub4Short+',
                    'address' => 'Jl. Teknologi No. 456',
                    'city' => 'Jakarta',
                    'postal_code' => '54321',
                    'country' => 'Indonesia',
                    'phone' => '+62 21 1234 5678',
                    'email' => '<EMAIL>',
                    'website' => 'https://sub4short.com'
                ],
                'items' => [
                    [
                        'description' => 'Gold Plan Subscription',
                        'period' => 'February 2024',
                        'quantity' => 1,
                        'unit_price' => 200000,
                        'total' => 200000
                    ]
                ],
                'subtotal' => 200000,
                'tax_rate' => 11,
                'tax_amount' => 22000,
                'total' => 222000,
                'payment_method' => 'Bank Transfer',
                'payment_date' => null,
                'notes' => 'Silakan lakukan pembayaran sebelum tanggal jatuh tempo.'
            ],
            'inv-003' => [
                'id' => 'inv-003',
                'number' => 'INV-2024-003',
                'date' => '2024-03-15',
                'due_date' => '2024-04-15',
                'status' => 'overdue',
                'customer' => [
                    'name' => 'PT. Digital Solutions',
                    'email' => '<EMAIL>',
                    'address' => 'Jl. Thamrin No. 999',
                    'city' => 'Surabaya',
                    'postal_code' => '11111',
                    'country' => 'Indonesia'
                ],
                'company' => [
                    'name' => 'Sub4Short+',
                    'address' => 'Jl. Teknologi No. 456',
                    'city' => 'Jakarta',
                    'postal_code' => '54321',
                    'country' => 'Indonesia',
                    'phone' => '+62 21 1234 5678',
                    'email' => '<EMAIL>',
                    'website' => 'https://sub4short.com'
                ],
                'items' => [
                    [
                        'description' => 'Bronze Plan Subscription',
                        'period' => 'March 2024',
                        'quantity' => 1,
                        'unit_price' => 50000,
                        'total' => 50000
                    ],
                    [
                        'description' => 'Additional Links Package',
                        'period' => 'March 2024',
                        'quantity' => 2,
                        'unit_price' => 25000,
                        'total' => 50000
                    ]
                ],
                'subtotal' => 100000,
                'tax_rate' => 11,
                'tax_amount' => 11000,
                'total' => 111000,
                'payment_method' => 'Bank Transfer',
                'payment_date' => null,
                'notes' => 'Invoice ini telah melewati tanggal jatuh tempo. Mohon segera lakukan pembayaran untuk menghindari penangguhan layanan.'
            ]
        ];

        return $invoices[$invoice_id] ?? null;
    }
}
