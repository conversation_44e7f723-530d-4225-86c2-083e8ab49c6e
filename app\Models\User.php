<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'profile_photo',
        'username',
        'bio',
        'google_id',
        'avatar',
        'email_verified_at',
        'notification_settings',
        'billing_settings',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'notification_settings' => 'array',
            'billing_settings' => 'array',
        ];
    }

    // Relationships
    public function links()
    {
        return $this->hasMany(Link::class);
    }

    public function clicks()
    {
        return $this->hasMany(Click::class);
    }

    // Dashboard Stats Methods
    public function getTotalLinksAttribute()
    {
        return $this->links()->count();
    }

    public function getTotalClicksAttribute()
    {
        return $this->links()->sum('clicks');
    }

    public function getTodayClicksAttribute()
    {
        return $this->clicks()->whereDate('created_at', today())->count();
    }

    public function getTotalEarningsAttribute()
    {
        return $this->links()->sum('earnings');
    }

    public function getMonthlyLinkLimitAttribute()
    {
        // Bronze plan limit
        return 100;
    }

    public function getLinksUsedThisMonthAttribute()
    {
        return $this->links()
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count();
    }

    public function getRemainingLinksAttribute()
    {
        return $this->monthly_link_limit - $this->links_used_this_month;
    }
}
