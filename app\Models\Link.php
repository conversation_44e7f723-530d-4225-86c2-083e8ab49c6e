<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Link extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'original_url',
        'short_code',
        'custom_alias',
        'title',
        'description',
        'clicks',
        'today_clicks',
        'earnings',
        'is_active',
        'last_clicked_at',
        'click_data'
    ];

    protected $casts = [
        'click_data' => 'array',
        'last_clicked_at' => 'datetime',
        'earnings' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function clicks()
    {
        return $this->hasMany(Click::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    // Accessors
    public function getShortUrlAttribute()
    {
        return url('/' . $this->short_code);
    }

    public function getTodayClicksAttribute()
    {
        return $this->clicks()->whereDate('created_at', Carbon::today())->count();
    }

    public function getTodayEarningsAttribute()
    {
        return $this->clicks()->whereDate('created_at', Carbon::today())->sum('earnings');
    }

    // Methods
    public function incrementClicks($earnings = 0)
    {
        $this->increment('clicks');
        $this->increment('earnings', $earnings);
        $this->update(['last_clicked_at' => now()]);
    }

    public function generateShortCode()
    {
        do {
            $code = $this->generateRandomString(6);
        } while (self::where('short_code', $code)->exists());

        return $code;
    }

    public static function generateUniqueShortCode()
    {
        do {
            $code = self::generateRandomStringStatic(6);
        } while (self::where('short_code', $code)->exists());

        return $code;
    }

    private function generateRandomString($length = 6)
    {
        return self::generateRandomStringStatic($length);
    }

    private static function generateRandomStringStatic($length = 6)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $randomString;
    }
}
