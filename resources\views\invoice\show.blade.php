<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice['number'] }} - Sub4Short+</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            .print-shadow { box-shadow: none !important; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <!-- Header Actions -->
        <div class="flex justify-between items-center mb-8 no-print">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Invoice {{ $invoice['number'] }}</h1>
                <p class="text-gray-600 mt-1">Generated on {{ date('d M Y') }}</p>
            </div>
            <div class="flex gap-3">
                <button onclick="window.print()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                    <i class="fa-solid fa-print"></i>
                    Print
                </button>
                <button onclick="downloadPDF()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2">
                    <i class="fa-solid fa-download"></i>
                    Download PDF
                </button>
                <a href="{{ url()->previous() }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2">
                    <i class="fa-solid fa-arrow-left"></i>
                    Back
                </a>
            </div>
        </div>

        <!-- Invoice Container -->
        <div class="bg-white rounded-2xl shadow-lg print-shadow overflow-hidden">
            <!-- Invoice Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-8">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="flex items-center gap-3 mb-4">
                            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                                <i class="fa-solid fa-link text-white text-xl"></i>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold">{{ $invoice['company']['name'] }}</h2>
                                <p class="text-blue-100">URL Shortener Service</p>
                            </div>
                        </div>
                        <div class="text-blue-100 text-sm space-y-1">
                            <p>{{ $invoice['company']['address'] }}</p>
                            <p>{{ $invoice['company']['city'] }}, {{ $invoice['company']['postal_code'] }}</p>
                            <p>{{ $invoice['company']['country'] }}</p>
                            <p class="mt-2">
                                <i class="fa-solid fa-phone w-4"></i> {{ $invoice['company']['phone'] }}
                            </p>
                            <p>
                                <i class="fa-solid fa-envelope w-4"></i> {{ $invoice['company']['email'] }}
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <h1 class="text-4xl font-bold mb-2">INVOICE</h1>
                        <div class="bg-white/20 rounded-lg p-4">
                            <p class="text-blue-100 text-sm">Invoice Number</p>
                            <p class="text-xl font-bold invoice-number hover:bg-white/10 px-2 py-1 rounded transition-colors">{{ $invoice['number'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Details -->
            <div class="p-8">
                <!-- Status Badge -->
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Bill To:</h3>
                        <div class="text-gray-700">
                            <p class="font-semibold">{{ $invoice['customer']['name'] }}</p>
                            <p>{{ $invoice['customer']['email'] }}</p>
                            <p>{{ $invoice['customer']['address'] }}</p>
                            <p>{{ $invoice['customer']['city'] }}, {{ $invoice['customer']['postal_code'] }}</p>
                            <p>{{ $invoice['customer']['country'] }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="mb-4">
                            @if($invoice['status'] === 'paid')
                                <span class="bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold">
                                    <i class="fa-solid fa-check-circle mr-1"></i>
                                    PAID
                                </span>
                            @elseif($invoice['status'] === 'pending')
                                <span class="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-semibold">
                                    <i class="fa-solid fa-clock mr-1"></i>
                                    PENDING
                                </span>
                            @else
                                <span class="bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-semibold">
                                    <i class="fa-solid fa-times-circle mr-1"></i>
                                    OVERDUE
                                </span>
                            @endif
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Invoice Date:</span>
                                <span class="font-semibold">{{ date('d M Y', strtotime($invoice['date'])) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Due Date:</span>
                                <span class="font-semibold">{{ date('d M Y', strtotime($invoice['due_date'])) }}</span>
                            </div>
                            @if($invoice['payment_date'])
                            <div class="flex justify-between">
                                <span class="text-gray-600">Payment Date:</span>
                                <span class="font-semibold text-green-600">{{ date('d M Y', strtotime($invoice['payment_date'])) }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="mb-8">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b-2 border-gray-200">
                                    <th class="text-left py-4 px-2 font-semibold text-gray-900">Description</th>
                                    <th class="text-center py-4 px-2 font-semibold text-gray-900">Qty</th>
                                    <th class="text-right py-4 px-2 font-semibold text-gray-900">Unit Price</th>
                                    <th class="text-right py-4 px-2 font-semibold text-gray-900">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($invoice['items'] as $item)
                                <tr class="border-b border-gray-100">
                                    <td class="py-4 px-2">
                                        <div>
                                            <p class="font-semibold text-gray-900">{{ $item['description'] }}</p>
                                            <p class="text-sm text-gray-600">{{ $item['period'] }}</p>
                                        </div>
                                    </td>
                                    <td class="text-center py-4 px-2 text-gray-700">{{ $item['quantity'] }}</td>
                                    <td class="text-right py-4 px-2 text-gray-700">Rp {{ number_format($item['unit_price'], 0, ',', '.') }}</td>
                                    <td class="text-right py-4 px-2 font-semibold text-gray-900">Rp {{ number_format($item['total'], 0, ',', '.') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Invoice Totals -->
                <div class="flex justify-end mb-8">
                    <div class="w-full max-w-sm">
                        <div class="bg-gray-50 rounded-lg p-6 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal:</span>
                                <span class="font-semibold">Rp {{ number_format($invoice['subtotal'], 0, ',', '.') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tax ({{ $invoice['tax_rate'] }}%):</span>
                                <span class="font-semibold">Rp {{ number_format($invoice['tax_amount'], 0, ',', '.') }}</span>
                            </div>
                            <hr class="border-gray-300">
                            <div class="flex justify-between text-lg">
                                <span class="font-bold text-gray-900">Total:</span>
                                <span class="font-bold text-blue-600">Rp {{ number_format($invoice['total'], 0, ',', '.') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                @if($invoice['status'] === 'paid')
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                    <div class="flex items-center gap-3 mb-3">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-check text-green-600"></i>
                        </div>
                        <h4 class="font-semibold text-green-900">Payment Received</h4>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-green-700">Payment Method:</span>
                            <span class="font-semibold ml-2">{{ $invoice['payment_method'] }}</span>
                        </div>
                        <div>
                            <span class="text-green-700">Payment Date:</span>
                            <span class="font-semibold ml-2">{{ date('d M Y', strtotime($invoice['payment_date'])) }}</span>
                        </div>
                    </div>
                </div>
                @elseif($invoice['status'] === 'pending')
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-clock text-yellow-600"></i>
                            </div>
                            <h4 class="font-semibold text-yellow-900">Payment Pending</h4>
                        </div>
                        <div class="no-print">
                            <button onclick="payNow()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                Pay Now
                            </button>
                        </div>
                    </div>
                    <p class="text-yellow-800 text-sm">Please complete your payment before the due date to avoid service interruption.</p>
                </div>
                @else
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-exclamation-triangle text-red-600"></i>
                            </div>
                            <h4 class="font-semibold text-red-900">Payment Overdue</h4>
                        </div>
                        <div class="no-print">
                            <button onclick="payNow()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm">
                                Pay Now
                            </button>
                        </div>
                    </div>
                    <p class="text-red-800 text-sm">This invoice is overdue. Please make payment immediately to avoid service suspension.</p>
                </div>
                @endif

                <!-- Notes -->
                @if($invoice['notes'])
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h4 class="font-semibold text-blue-900 mb-2">Notes:</h4>
                    <p class="text-blue-800">{{ $invoice['notes'] }}</p>
                </div>
                @endif
            </div>

            <!-- Footer -->
            <div class="bg-gray-50 px-8 py-6 border-t border-gray-200">
                <div class="text-center text-sm text-gray-600">
                    <p class="mb-2">Thank you for your business!</p>
                    <p>For questions about this invoice, please contact us at {{ $invoice['company']['email'] }}</p>
                    <p class="mt-2">{{ $invoice['company']['website'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadPDF() {
            // Show loading state
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Generating...';
            btn.disabled = true;

            // Simulate PDF generation
            setTimeout(() => {
                // In a real application, this would make an API call to generate PDF
                // For now, we'll use the browser's print to PDF functionality
                window.print();

                // Reset button
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 1000);
        }

        // Add print styles dynamically
        function addPrintStyles() {
            const style = document.createElement('style');
            style.textContent = `
                @media print {
                    @page {
                        margin: 0.5in;
                        size: A4;
                    }
                    body {
                        font-size: 12px;
                        line-height: 1.4;
                    }
                    .print-break {
                        page-break-before: always;
                    }
                    .print-avoid-break {
                        page-break-inside: avoid;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // Initialize print styles
        addPrintStyles();

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+P for print
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Ctrl+D for download
            if (e.ctrlKey && e.key === 'd') {
                e.preventDefault();
                downloadPDF();
            }
        });

        // Add copy invoice number functionality
        function copyInvoiceNumber() {
            const invoiceNumber = '{{ $invoice["number"] }}';
            navigator.clipboard.writeText(invoiceNumber).then(() => {
                showToast('Invoice number copied to clipboard!');
            });
        }

        // Toast notification
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // Payment functionality
        function payNow() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-gray-900">Payment Options</h3>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fa-solid fa-times"></i>
                        </button>
                    </div>
                    <div class="space-y-3 mb-6">
                        <button onclick="selectPayment('credit-card')" class="w-full flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fa-solid fa-credit-card text-blue-600"></i>
                            <div class="text-left">
                                <p class="font-semibold">Credit Card</p>
                                <p class="text-sm text-gray-600">Pay instantly with your credit card</p>
                            </div>
                        </button>
                        <button onclick="selectPayment('bank-transfer')" class="w-full flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fa-solid fa-university text-green-600"></i>
                            <div class="text-left">
                                <p class="font-semibold">Bank Transfer</p>
                                <p class="text-sm text-gray-600">Transfer to our bank account</p>
                            </div>
                        </button>
                        <button onclick="selectPayment('e-wallet')" class="w-full flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
                            <i class="fa-solid fa-mobile-alt text-purple-600"></i>
                            <div class="text-left">
                                <p class="font-semibold">E-Wallet</p>
                                <p class="text-sm text-gray-600">Pay with Dana, OVO, or GoPay</p>
                            </div>
                        </button>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold">Total Amount:</span>
                            <span class="text-xl font-bold text-blue-600">Rp {{ number_format($invoice['total'], 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            window.currentModal = modal;
        }

        function selectPayment(method) {
            showToast('Redirecting to payment gateway...');
            closeModal();
            // In real app, this would redirect to payment gateway
            setTimeout(() => {
                showToast('Payment functionality would be implemented here');
            }, 1000);
        }

        function closeModal() {
            if (window.currentModal) {
                document.body.removeChild(window.currentModal);
                window.currentModal = null;
            }
        }

        // Add click to copy functionality to invoice number
        document.addEventListener('DOMContentLoaded', function() {
            const invoiceNumber = document.querySelector('.invoice-number');
            if (invoiceNumber) {
                invoiceNumber.style.cursor = 'pointer';
                invoiceNumber.title = 'Click to copy';
                invoiceNumber.addEventListener('click', copyInvoiceNumber);
            }

            // Close modal on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && window.currentModal) {
                    closeModal();
                }
            });

            // Close modal on backdrop click
            document.addEventListener('click', function(e) {
                if (window.currentModal && e.target === window.currentModal) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>
