<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Link;
use App\Models\Click;
use Carbon\Carbon;

class LinkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user or create one
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'username' => 'demouser',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        // Sample links data
        $linksData = [
            [
                'original_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'title' => 'Never Gonna Give You Up - Rick <PERSON>',
                'description' => 'Official music video',
                'clicks' => 245,
                'earnings' => 12.25,
            ],
            [
                'original_url' => 'https://github.com/laravel/laravel',
                'title' => 'Laravel Framework',
                'description' => 'The Laravel Framework repository',
                'clicks' => 189,
                'earnings' => 9.45,
            ],
            [
                'original_url' => 'https://tailwindcss.com/docs',
                'title' => 'Tailwind CSS Documentation',
                'description' => 'Utility-first CSS framework',
                'clicks' => 156,
                'earnings' => 7.80,
            ],
            [
                'original_url' => 'https://www.google.com/search?q=laravel+tutorial',
                'title' => 'Laravel Tutorial Search',
                'description' => 'Google search for Laravel tutorials',
                'clicks' => 98,
                'earnings' => 4.90,
            ],
            [
                'original_url' => 'https://stackoverflow.com/questions/tagged/php',
                'title' => 'PHP Questions - Stack Overflow',
                'description' => 'PHP related questions and answers',
                'clicks' => 67,
                'earnings' => 3.35,
            ],
            [
                'original_url' => 'https://www.php.net/manual/en/',
                'title' => 'PHP Manual',
                'description' => 'Official PHP documentation',
                'clicks' => 45,
                'earnings' => 2.25,
            ],
            [
                'original_url' => 'https://vuejs.org/guide/',
                'title' => 'Vue.js Guide',
                'description' => 'The Progressive JavaScript Framework',
                'clicks' => 34,
                'earnings' => 1.70,
            ],
            [
                'original_url' => 'https://reactjs.org/docs/getting-started.html',
                'title' => 'React Getting Started',
                'description' => 'A JavaScript library for building user interfaces',
                'clicks' => 28,
                'earnings' => 1.40,
            ],
            [
                'original_url' => 'https://nodejs.org/en/docs/',
                'title' => 'Node.js Documentation',
                'description' => 'JavaScript runtime built on Chrome V8',
                'clicks' => 23,
                'earnings' => 1.15,
            ],
            [
                'original_url' => 'https://www.mysql.com/documentation/',
                'title' => 'MySQL Documentation',
                'description' => 'The world most popular open source database',
                'clicks' => 19,
                'earnings' => 0.95,
            ]
        ];

        foreach ($linksData as $index => $linkData) {
            $link = Link::create([
                'user_id' => $user->id,
                'original_url' => $linkData['original_url'],
                'short_code' => $this->generateShortCode(),
                'custom_alias' => null,
                'title' => $linkData['title'],
                'description' => $linkData['description'],
                'clicks' => $linkData['clicks'],
                'earnings' => $linkData['earnings'],
                'is_active' => true,
                'last_clicked_at' => Carbon::now()->subHours(rand(1, 48)),
                'created_at' => Carbon::now()->subDays(rand(0, 7)), // More recent links
            ]);

            // Create click records for each link
            $this->createClicksForLink($link, $user->id);
        }

        // Create some very recent links for activity feed
        $recentLinksData = [
            [
                'original_url' => 'https://www.instagram.com/p/example123',
                'title' => 'Instagram Post - Amazing Sunset',
                'description' => 'Beautiful sunset photography',
                'clicks' => rand(5, 25),
                'earnings' => rand(25, 125) / 100,
                'created_at' => Carbon::now()->subMinutes(rand(5, 120))
            ],
            [
                'original_url' => 'https://twitter.com/user/status/123456789',
                'title' => 'Twitter Thread - Tech Tips',
                'description' => 'Useful programming tips and tricks',
                'clicks' => rand(3, 15),
                'earnings' => rand(15, 75) / 100,
                'created_at' => Carbon::now()->subMinutes(rand(30, 180))
            ],
            [
                'original_url' => 'https://medium.com/@author/article-title',
                'title' => 'Medium Article - Web Development',
                'description' => 'Modern web development practices',
                'clicks' => rand(8, 30),
                'earnings' => rand(40, 150) / 100,
                'created_at' => Carbon::now()->subHours(rand(1, 6))
            ]
        ];

        foreach ($recentLinksData as $linkData) {
            $link = Link::create([
                'user_id' => $user->id,
                'original_url' => $linkData['original_url'],
                'short_code' => $this->generateShortCode(),
                'custom_alias' => null,
                'title' => $linkData['title'],
                'description' => $linkData['description'],
                'clicks' => $linkData['clicks'],
                'earnings' => $linkData['earnings'],
                'is_active' => true,
                'last_clicked_at' => $linkData['created_at']->addMinutes(rand(5, 60)),
                'created_at' => $linkData['created_at'],
            ]);

            // Create recent clicks for activity
            $this->createRecentClicksForLink($link, $user->id);
        }
    }

    private function generateShortCode()
    {
        do {
            $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            $code = '';
            for ($i = 0; $i < 6; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (Link::where('short_code', $code)->exists());

        return $code;
    }

    private function createClicksForLink($link, $userId)
    {
        $totalClicks = $link->clicks;
        $countries = ['Indonesia', 'Malaysia', 'Singapore', 'Thailand', 'Philippines'];
        $cities = ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang'];
        $devices = ['mobile', 'desktop', 'tablet'];
        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
        $os = ['Windows', 'Android', 'iOS', 'macOS', 'Linux'];

        // Create clicks spread over the last 30 days
        for ($i = 0; $i < $totalClicks; $i++) {
            $clickDate = Carbon::now()->subDays(rand(0, 30));
            $earnings = rand(3, 8) / 100; // 0.03 to 0.08

            Click::create([
                'link_id' => $link->id,
                'user_id' => $userId,
                'ip_address' => $this->generateRandomIP(),
                'user_agent' => 'Mozilla/5.0 (compatible; Bot)',
                'referer' => rand(0, 1) ? 'https://google.com' : null,
                'country' => $countries[array_rand($countries)],
                'city' => $cities[array_rand($cities)],
                'device_type' => $devices[array_rand($devices)],
                'browser' => $browsers[array_rand($browsers)],
                'os' => $os[array_rand($os)],
                'earnings' => $earnings,
                'is_unique' => rand(0, 1),
                'created_at' => $clickDate,
                'updated_at' => $clickDate,
            ]);
        }
    }

    private function generateRandomIP()
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    private function createRecentClicksForLink($link, $userId)
    {
        $totalClicks = $link->clicks;
        $countries = ['Indonesia', 'Malaysia', 'Singapore', 'Thailand', 'Philippines'];
        $cities = ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang'];
        $devices = ['mobile', 'desktop', 'tablet'];
        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
        $os = ['Windows', 'Android', 'iOS', 'macOS', 'Linux'];

        // Create clicks spread over the last few hours
        for ($i = 0; $i < $totalClicks; $i++) {
            $clickDate = Carbon::now()->subMinutes(rand(5, 360)); // Last 6 hours
            $earnings = rand(3, 8) / 100; // 0.03 to 0.08

            Click::create([
                'link_id' => $link->id,
                'user_id' => $userId,
                'ip_address' => $this->generateRandomIP(),
                'user_agent' => 'Mozilla/5.0 (compatible; Bot)',
                'referer' => rand(0, 1) ? 'https://google.com' : null,
                'country' => $countries[array_rand($countries)],
                'city' => $cities[array_rand($cities)],
                'device_type' => $devices[array_rand($devices)],
                'browser' => $browsers[array_rand($browsers)],
                'os' => $os[array_rand($os)],
                'earnings' => $earnings,
                'is_unique' => rand(0, 1),
                'created_at' => $clickDate,
                'updated_at' => $clickDate,
            ]);
        }
    }
}
