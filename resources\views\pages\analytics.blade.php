<!-- Header -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 sm:mb-8">
    <div>
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Analytics</h1>
        <p class="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">Statistik dan performa shortlink Anda</p>
    </div>
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
        <select class="px-3 sm:px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-sm">
            <option>7 hari terakhir</option>
            <option>30 hari terakhir</option>
            <option>90 hari terakhir</option>
        </select>
        <button class="bg-blue-600 dark:bg-blue-500 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 flex items-center justify-center text-sm font-medium">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export
        </button>
    </div>
</div>

<!-- Overview Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <p class="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Total Klik</p>
                <p class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mt-1">2,140</p>
                <p class="text-xs sm:text-sm text-green-600 dark:text-green-400 mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                    </svg>
                    +12.5%
                </p>
            </div>
            <div class="p-2 sm:p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 flex-shrink-0">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                </svg>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <p class="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Pendapatan</p>
                <p class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mt-1">Rp 10,700</p>
                <p class="text-xs sm:text-sm text-green-600 dark:text-green-400 mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                    </svg>
                    +8.3%
                </p>
            </div>
            <div class="p-2 sm:p-3 rounded-full bg-green-100 dark:bg-green-900/30 flex-shrink-0">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <p class="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">CTR Rata-rata</p>
                <p class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mt-1">3.2%</p>
                <p class="text-xs sm:text-sm text-red-600 dark:text-red-400 mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                    </svg>
                    -2.1%
                </p>
            </div>
            <div class="p-2 sm:p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex-shrink-0">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <p class="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Shortlink Aktif</p>
                <p class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mt-1">24</p>
                <p class="text-xs sm:text-sm text-green-600 dark:text-green-400 mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                    </svg>
                    +2 baru
                </p>
            </div>
            <div class="p-2 sm:p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 flex-shrink-0">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8">
    <!-- Click Trend Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6">
            <h3 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">Trend Klik</h3>
            <div class="flex items-center space-x-2">
                <span class="w-3 h-3 bg-blue-500 rounded-full"></span>
                <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Klik</span>
            </div>
        </div>
        <div class="h-48 sm:h-64 flex items-end justify-between space-x-1 sm:space-x-2">
            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-t" style="height: 60%"></div>
            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-t" style="height: 80%"></div>
            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-t" style="height: 45%"></div>
            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-t" style="height: 90%"></div>
            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-t" style="height: 70%"></div>
            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-t" style="height: 85%"></div>
            <div class="flex-1 bg-blue-500 rounded-t" style="height: 95%"></div>
        </div>
        <div class="flex justify-between mt-3 sm:mt-4 text-xs text-gray-500 dark:text-gray-400">
            <span class="hidden sm:inline">Senin</span>
            <span class="sm:hidden">Sen</span>
            <span class="hidden sm:inline">Selasa</span>
            <span class="sm:hidden">Sel</span>
            <span class="hidden sm:inline">Rabu</span>
            <span class="sm:hidden">Rab</span>
            <span class="hidden sm:inline">Kamis</span>
            <span class="sm:hidden">Kam</span>
            <span class="hidden sm:inline">Jumat</span>
            <span class="sm:hidden">Jum</span>
            <span class="hidden sm:inline">Sabtu</span>
            <span class="sm:hidden">Sab</span>
            <span class="hidden sm:inline">Minggu</span>
            <span class="sm:hidden">Min</span>
        </div>
    </div>
    
    <!-- Top Performing Links -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
        <h3 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4 sm:mb-6">Top Performing Links</h3>
        <div class="space-y-3 sm:space-y-4">
            <div class="flex items-center justify-between p-3 sm:p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-all duration-200">
                <div class="flex items-center min-w-0 flex-1">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <div class="ml-3 min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white truncate">sub4short.plus/abc123</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">1,250 klik</p>
                    </div>
                </div>
                <div class="text-right flex-shrink-0 ml-3">
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">Rp 6,250</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">+15%</p>
                </div>
            </div>
            
            <div class="flex items-center justify-between p-3 sm:p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-all duration-200">
                <div class="flex items-center min-w-0 flex-1">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <div class="ml-3 min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white truncate">sub4short.plus/xyz789</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">890 klik</p>
                    </div>
                </div>
                <div class="text-right flex-shrink-0 ml-3">
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">Rp 4,450</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">+8%</p>
                </div>
            </div>
            
            <div class="flex items-center justify-between p-3 sm:p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-all duration-200">
                <div class="flex items-center min-w-0 flex-1">
                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <div class="ml-3 min-w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white truncate">sub4short.plus/def456</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">567 klik</p>
                    </div>
                </div>
                <div class="text-right flex-shrink-0 ml-3">
                    <p class="text-sm font-medium text-green-600 dark:text-green-400">Rp 2,835</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">+5%</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Geographic Distribution -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Distribusi Geografis</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fa-solid fa-flag text-blue-600 dark:text-blue-400 text-xl"></i>
            </div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Indonesia</h4>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">65%</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">1,391 klik</p>
        </div>
        
        <div class="text-center">
            <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fa-solid fa-flag text-green-600 dark:text-green-400 text-xl"></i>
            </div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Malaysia</h4>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">15%</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">321 klik</p>
        </div>
        
        <div class="text-center">
            <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fa-solid fa-flag text-yellow-600 dark:text-yellow-400 text-xl"></i>
            </div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Singapura</h4>
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">12%</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">257 klik</p>
        </div>
        
        <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fa-solid fa-flag text-purple-600 dark:text-purple-400 text-xl"></i>
            </div>
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Lainnya</h4>
            <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">8%</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">171 klik</p>
        </div>
    </div>
</div> 