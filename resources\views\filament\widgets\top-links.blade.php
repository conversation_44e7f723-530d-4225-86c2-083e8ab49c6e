<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Top Performing Links
        </x-slot>

        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full text-left">
                    <thead>
                        <tr class="border-b border-gray-200 dark:border-gray-700">
                            <th class="pb-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Short URL</th>
                            <th class="pb-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Original URL</th>
                            <th class="pb-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-center">Clicks</th>
                            <th class="pb-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-center">Revenue</th>
                            <th class="pb-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-center">CTR</th>
                            <th class="pb-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-center">Created</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($links as $link)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center mr-3">
                                            <x-heroicon-o-link class="w-4 h-4 text-primary-600 dark:text-primary-400" />
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $link['short_url'] }}</p>
                                            <button onclick="navigator.clipboard.writeText('{{ $link['short_url'] }}')" 
                                                    class="text-xs text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                                Click to copy
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 truncate max-w-xs" title="{{ $link['original_url'] }}">
                                        {{ $link['original_url'] }}
                                    </p>
                                </td>
                                <td class="py-3 text-center">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300">
                                        {{ number_format($link['clicks']) }}
                                    </span>
                                </td>
                                <td class="py-3 text-center">
                                    <span class="text-sm font-medium text-success-600 dark:text-success-400">
                                        Rp {{ number_format($link['revenue']) }}
                                    </span>
                                </td>
                                <td class="py-3 text-center">
                                    <span @class([
                                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                        'bg-success-100 text-success-800 dark:bg-success-900/30 dark:text-success-300' => $link['ctr'] >= 3.0,
                                        'bg-warning-100 text-warning-800 dark:bg-warning-900/30 dark:text-warning-300' => $link['ctr'] >= 2.0 && $link['ctr'] < 3.0,
                                        'bg-danger-100 text-danger-800 dark:bg-danger-900/30 dark:text-danger-300' => $link['ctr'] < 2.0,
                                    ])>
                                        {{ number_format($link['ctr'], 1) }}%
                                    </span>
                                </td>
                                <td class="py-3 text-center">
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        <p>{{ $link['created_at']->format('M d, Y') }}</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-500">{{ $link['created_at']->diffForHumans() }}</p>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            @if(empty($links))
                <div class="text-center py-12">
                    <x-heroicon-o-link class="w-12 h-12 text-gray-400 dark:text-gray-600 mx-auto mb-4" />
                    <p class="text-sm text-gray-500 dark:text-gray-400">No links found</p>
                </div>
            @endif
        </div>
        
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <a href="#" class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
                View all links →
            </a>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
