<?php

namespace App\Filament\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class TopLinksWidget extends BaseWidget
{
    protected static ?string $heading = 'Top Performing Links';
    
    protected static ?int $sort = 3;
    
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                // Dummy query - replace with your actual model
                collect([
                    (object) [
                        'id' => 1,
                        'short_url' => 'sub4short.plus/abc123',
                        'original_url' => 'https://example.com/very-long-url-example',
                        'clicks' => 1250,
                        'revenue' => 6250,
                        'ctr' => 3.2,
                        'created_at' => now()->subDays(5),
                    ],
                    (object) [
                        'id' => 2,
                        'short_url' => 'sub4short.plus/xyz789',
                        'original_url' => 'https://another-example.com/another-long-url',
                        'clicks' => 890,
                        'revenue' => 4450,
                        'ctr' => 2.8,
                        'created_at' => now()->subDays(3),
                    ],
                    (object) [
                        'id' => 3,
                        'short_url' => 'sub4short.plus/def456',
                        'original_url' => 'https://third-example.com/some-url',
                        'clicks' => 567,
                        'revenue' => 2835,
                        'ctr' => 4.1,
                        'created_at' => now()->subDays(1),
                    ],
                    (object) [
                        'id' => 4,
                        'short_url' => 'sub4short.plus/ghi789',
                        'original_url' => 'https://fourth-example.com/example-url',
                        'clicks' => 423,
                        'revenue' => 2115,
                        'ctr' => 2.5,
                        'created_at' => now()->subHours(12),
                    ],
                    (object) [
                        'id' => 5,
                        'short_url' => 'sub4short.plus/jkl012',
                        'original_url' => 'https://fifth-example.com/sample-url',
                        'clicks' => 298,
                        'revenue' => 1490,
                        'ctr' => 3.7,
                        'created_at' => now()->subHours(6),
                    ],
                ])
            )
            ->columns([
                Tables\Columns\TextColumn::make('short_url')
                    ->label('Short URL')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('URL copied!')
                    ->weight('medium'),
                    
                Tables\Columns\TextColumn::make('original_url')
                    ->label('Original URL')
                    ->limit(50)
                    ->tooltip(function ($record) {
                        return $record->original_url;
                    }),
                    
                Tables\Columns\TextColumn::make('clicks')
                    ->label('Clicks')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                    
                Tables\Columns\TextColumn::make('revenue')
                    ->label('Revenue')
                    ->money('IDR')
                    ->sortable()
                    ->color('success'),
                    
                Tables\Columns\TextColumn::make('ctr')
                    ->label('CTR (%)')
                    ->numeric(2)
                    ->sortable()
                    ->badge()
                    ->color(fn ($state) => $state >= 3.0 ? 'success' : ($state >= 2.0 ? 'warning' : 'danger')),
                    
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->since(),
            ])
            ->defaultSort('clicks', 'desc')
            ->paginated(false);
    }
}
