<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;

class TopLinksWidget extends Widget
{
    protected static string $view = 'filament.widgets.top-links';

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    protected function getViewData(): array
    {
        return [
            'links' => [
                [
                    'id' => 1,
                    'short_url' => 'sub4short.plus/abc123',
                    'original_url' => 'https://example.com/very-long-url-example',
                    'clicks' => 1250,
                    'revenue' => 6250,
                    'ctr' => 3.2,
                    'created_at' => now()->subDays(5),
                ],
                [
                    'id' => 2,
                    'short_url' => 'sub4short.plus/xyz789',
                    'original_url' => 'https://another-example.com/another-long-url',
                    'clicks' => 890,
                    'revenue' => 4450,
                    'ctr' => 2.8,
                    'created_at' => now()->subDays(3),
                ],
                [
                    'id' => 3,
                    'short_url' => 'sub4short.plus/def456',
                    'original_url' => 'https://third-example.com/some-url',
                    'clicks' => 567,
                    'revenue' => 2835,
                    'ctr' => 4.1,
                    'created_at' => now()->subDays(1),
                ],
                [
                    'id' => 4,
                    'short_url' => 'sub4short.plus/ghi789',
                    'original_url' => 'https://fourth-example.com/example-url',
                    'clicks' => 423,
                    'revenue' => 2115,
                    'ctr' => 2.5,
                    'created_at' => now()->subHours(12),
                ],
                [
                    'id' => 5,
                    'short_url' => 'sub4short.plus/jkl012',
                    'original_url' => 'https://fifth-example.com/sample-url',
                    'clicks' => 298,
                    'revenue' => 1490,
                    'ctr' => 3.7,
                    'created_at' => now()->subHours(6),
                ],
            ]
        ];
    }
}
