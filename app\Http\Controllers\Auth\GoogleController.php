<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Socialite\Facades\Socialite;

class GoogleController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirectToGoogle()
    {
        try {
            \Log::info('Google OAuth redirect initiated', [
                'client_id' => config('services.google.client_id'),
                'redirect_uri' => config('services.google.redirect'),
                'app_url' => config('app.url')
            ]);

            return Socialite::driver('google')
                ->scopes(['openid', 'profile', 'email'])
                ->redirect();

        } catch (\Exception $e) {
            \Log::error('Google OAuth redirect error: ' . $e->getMessage());
            return redirect('/auth/login')->with('error', 'Konfigurasi Google OAuth bermasalah. Silakan hubungi administrator.');
        }
    }

    /**
     * Handle Google OAuth callback
     */
    public function handleGoogleCallback()
    {
        try {
            \Log::info('Google OAuth callback received');
            $googleUser = Socialite::driver('google')->user();
            \Log::info('Google user data received', [
                'id' => $googleUser->getId(),
                'email' => $googleUser->getEmail(),
                'name' => $googleUser->getName()
            ]);

            // Check if user already exists
            $user = User::where('email', $googleUser->getEmail())->first();

            if ($user) {
                \Log::info('Existing user found', ['user_id' => $user->id]);
                // Update user with Google info if not already set
                if (!$user->google_id) {
                    $user->update([
                        'google_id' => $googleUser->getId(),
                        'avatar' => $googleUser->getAvatar(),
                    ]);
                    \Log::info('Updated existing user with Google info');
                }
            } else {
                \Log::info('Creating new user from Google data');
                // Create new user
                $user = User::create([
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                    'email_verified_at' => now(),
                    'password' => Hash::make(uniqid()), // Random password since they'll use Google
                ]);
                \Log::info('New user created', ['user_id' => $user->id]);
            }

            // Login the user
            Auth::login($user, true);
            \Log::info('User logged in successfully', ['user_id' => $user->id]);

            // Redirect to bronze dashboard
            return redirect()->intended('/bronze/dashboard')->with('success', 'Berhasil masuk dengan Google!');

        } catch (\Exception $e) {
            \Log::error('Google OAuth error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return redirect('/auth/login')->with('error', 'Terjadi kesalahan saat masuk dengan Google. Silakan coba lagi.');
        }
    }
}
