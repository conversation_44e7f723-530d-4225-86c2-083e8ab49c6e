<div x-data="shortlinkData()">
<!-- Header -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 sm:mb-8">
    <div>
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Kelola Shortlink</h1>
        <p class="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">Buat dan kelola shortlink Anda</p>
    </div>
    <div class="flex flex-col sm:flex-row gap-3">
        <button onclick="refreshLinks()" class="w-full sm:w-auto bg-gray-600 dark:bg-gray-500 text-white px-4 py-3 rounded-lg font-semibold hover:bg-gray-700 dark:hover:bg-gray-600 transition-all duration-200 flex items-center justify-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span class="hidden sm:inline">Refresh</span>
        </button>
        <button @click="showShortlinkForm = true" class="w-full sm:w-auto bg-blue-600 dark:bg-blue-500 text-white px-4 sm:px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 flex items-center justify-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span class="hidden sm:inline">Buat Shortlink</span>
            <span class="sm:hidden">Buat Link Baru</span>
        </button>
    </div>
</div>

<!-- Create Shortlink Form -->
<div x-show="showShortlinkForm"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform scale-95"
     x-transition:enter-end="opacity-100 transform scale-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform scale-100"
     x-transition:leave-end="opacity-0 transform scale-95"
     class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6 mb-6 sm:mb-8 transition-all duration-300">
    <div class="flex items-center justify-between mb-4 sm:mb-6">
        <h2 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">Buat Shortlink Baru</h2>
        <button @click="showShortlinkForm = false; sidebarOpen = false" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    
    <form @submit.prevent="createShortlink()" class="space-y-4 sm:space-y-6">
        <div class="space-y-4 sm:space-y-0 sm:grid sm:grid-cols-1 lg:grid-cols-2 sm:gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    URL Asli
                </label>
                <input type="url" x-model="newShortlink.url" placeholder="https://example.com/very-long-url" required
                       class="w-full px-3 sm:px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 text-sm sm:text-base">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    Custom Alias <span class="text-gray-500 text-xs">(Opsional)</span>
                </label>
                <input type="text" x-model="newShortlink.alias" placeholder="my-custom-link"
                       class="w-full px-3 sm:px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 text-sm sm:text-base">
            </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
            <button type="button" @click="showShortlinkForm = false; sidebarOpen = false"
                    class="w-full sm:w-auto px-4 sm:px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 font-medium">
                Batal
            </button>
            <button type="submit"
                    class="w-full sm:w-auto px-4 sm:px-6 py-3 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 font-medium flex items-center justify-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1M9 6h6"></path>
                </svg>
                Buat Shortlink
            </button>
        </div>
    </form>
</div>

<!-- Search and Filter -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 mb-6">
    <div class="flex flex-col sm:flex-row gap-4">
        <!-- Search -->
        <div class="flex-1">
            <div class="relative">
                <input
                    type="text"
                    x-model="searchQuery"
                    @input="debounceSearch()"
                    placeholder="Cari berdasarkan judul, URL, atau kode link..."
                    class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Status Filter -->
        <div class="sm:w-48">
            <select
                x-model="statusFilter"
                @change="loadLinks()"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
                <option value="">Semua Status</option>
                <option value="active">Aktif</option>
                <option value="inactive">Nonaktif</option>
            </select>
        </div>

        <!-- Per Page -->
        <div class="sm:w-32">
            <select
                x-model="perPage"
                @change="loadLinks()"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
        </div>
    </div>
</div>

<!-- Shortlinks Table -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300">
    <div class="px-4 sm:px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Daftar Shortlink</h2>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                <span class="hidden sm:inline">Total: </span><span x-text="pagination.total || 0"></span> link
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Shortlink</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">URL Asli</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Klik</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Pendapatan</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Aksi</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Loading State -->
                <template x-if="loadingLinks">
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                                <p class="text-gray-500 dark:text-gray-400">Memuat data...</p>
                            </div>
                        </td>
                    </tr>
                </template>

                <!-- Empty State -->
                <template x-if="!loadingLinks && links.length === 0">
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                                    <i class="fa-solid fa-link text-gray-400 dark:text-gray-500 text-xl"></i>
                                </div>
                                <p class="text-gray-500 dark:text-gray-400 text-lg font-medium mb-2">Belum ada shortlink</p>
                                <p class="text-gray-400 dark:text-gray-500 text-sm mb-4">Buat shortlink pertama Anda untuk memulai</p>
                                <button @click="showShortlinkForm = true" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    Buat Shortlink
                                </button>
                            </div>
                        </td>
                    </tr>
                </template>

                <!-- Links Data -->
                <template x-for="link in links" :key="link.id">
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10 rounded-lg flex items-center justify-center"
                                     :class="link.is_active ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-gray-100 dark:bg-gray-700'">
                                    <i class="fa-solid fa-link"
                                       :class="link.is_active ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500'"></i>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white" x-text="link.short_url"></div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400" x-text="'Dibuat ' + link.created_at_human"></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 dark:text-white max-w-xs truncate" x-text="link.original_url" :title="link.original_url"></div>
                            <div class="text-sm text-gray-500 dark:text-gray-400" x-text="link.title || 'Tidak ada judul'"></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900 dark:text-white" x-text="link.clicks.toLocaleString()"></div>
                            <div class="text-sm text-gray-500 dark:text-gray-400" x-text="'+' + link.today_clicks + ' hari ini'"></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-green-600 dark:text-green-400" x-text="'Rp ' + link.earnings.toLocaleString()"></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                  :class="link.is_active ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200'"
                                  x-text="link.is_active ? 'Aktif' : 'Nonaktif'">
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <button @click="copyLink(link.short_url)"
                                        class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200"
                                        title="Copy Link">
                                    <i class="fa-solid fa-copy"></i>
                                </button>
                                <button @click="editLink(link)"
                                        class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 transition-colors duration-200"
                                        title="Edit Link">
                                    <i class="fa-solid fa-edit"></i>
                                </button>
                                <button @click="toggleLinkStatus(link)"
                                        class="transition-colors duration-200"
                                        :class="link.is_active ? 'text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300' : 'text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300'"
                                        :title="link.is_active ? 'Nonaktifkan' : 'Aktifkan'">
                                    <i :class="link.is_active ? 'fa-solid fa-pause' : 'fa-solid fa-play'"></i>
                                </button>
                                <button @click="deleteLink(link)"
                                        class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200"
                                        title="Hapus Link">
                                    <i class="fa-solid fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div x-show="!loadingLinks && links.length > 0" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="text-sm text-gray-700 dark:text-gray-300">
                Menampilkan <span x-text="pagination.from || 0"></span> sampai <span x-text="pagination.to || 0"></span> dari <span x-text="pagination.total || 0"></span> hasil
            </div>
            <div class="flex items-center space-x-2">
                <button @click="changePage(pagination.current_page - 1)"
                        :disabled="pagination.current_page <= 1"
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                    Sebelumnya
                </button>

                <template x-for="page in getPageNumbers()" :key="page">
                    <button @click="changePage(page)"
                            :class="page === pagination.current_page ? 'bg-blue-600 text-white' : 'bg-white text-gray-500 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700'"
                            class="px-3 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600 rounded-md"
                            x-text="page">
                    </button>
                </template>

                <button @click="changePage(pagination.current_page + 1)"
                        :disabled="pagination.current_page >= pagination.last_page"
                        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
                    Selanjutnya
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Link Modal -->
<div x-show="showEditModal" class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" @click="showEditModal = false">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                            Edit Shortlink
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Judul</label>
                                <input type="text" x-model="editingLink.title"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Deskripsi</label>
                                <textarea x-model="editingLink.description" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"></textarea>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" x-model="editingLink.is_active"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label class="ml-2 block text-sm text-gray-900 dark:text-white">
                                    Link aktif
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button @click="saveEditLink()"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    Simpan
                </button>
                <button @click="showEditModal = false"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-700">
                    Batal
                </button>
            </div>
        </div>
    </div>
</div>
</div>

<script>
// Initialize shortlink data
document.addEventListener('alpine:init', () => {
    Alpine.data('shortlinkData', () => ({
        links: [],
        pagination: {},
        loadingLinks: true,
        searchQuery: '',
        statusFilter: '',
        perPage: 10,
        currentPage: 1,
        searchTimeout: null,
        showEditModal: false,
        editingLink: {},

        init() {
            this.loadLinks();
        },

        async loadLinks() {
            this.loadingLinks = true;
            try {
                const params = new URLSearchParams({
                    page: this.currentPage,
                    per_page: this.perPage,
                    search: this.searchQuery,
                    status: this.statusFilter
                });

                const response = await fetch(`/api/links?${params}`, {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        this.links = data.links;
                        this.pagination = data.pagination;
                    } else {
                        console.error('Links API returned non-JSON response');
                        // Check if redirected to login
                        if (response.status === 401 || response.url.includes('login')) {
                            window.location.href = '/login';
                            return;
                        }
                        showToast('Terjadi kesalahan saat memuat data', 'error');
                    }
                } else {
                    console.error('Failed to load links:', response.status);
                    if (response.status === 401) {
                        window.location.href = '/login';
                        return;
                    }
                    showToast('Gagal memuat data shortlink', 'error');
                }
            } catch (error) {
                console.error('Error loading links:', error);
                showToast('Terjadi kesalahan jaringan', 'error');
            } finally {
                this.loadingLinks = false;
            }
        },

        debounceSearch() {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.currentPage = 1;
                this.loadLinks();
            }, 500);
        },

        changePage(page) {
            if (page >= 1 && page <= this.pagination.last_page) {
                this.currentPage = page;
                this.loadLinks();
            }
        },

        getPageNumbers() {
            const pages = [];
            const current = this.pagination.current_page;
            const last = this.pagination.last_page;

            for (let i = Math.max(1, current - 2); i <= Math.min(last, current + 2); i++) {
                pages.push(i);
            }
            return pages;
        },

        async copyLink(url) {
            try {
                await navigator.clipboard.writeText(url);
                showToast('Link berhasil disalin!', 'success');
            } catch (error) {
                showToast('Gagal menyalin link', 'error');
            }
        },

        editLink(link) {
            this.editingLink = { ...link };
            this.showEditModal = true;
        },

        async saveEditLink() {
            try {
                const response = await fetch(`/api/links/${this.editingLink.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        title: this.editingLink.title,
                        description: this.editingLink.description,
                        is_active: this.editingLink.is_active
                    })
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        showToast('Link berhasil diperbarui!', 'success');
                        this.showEditModal = false;
                        this.loadLinks();
                    } else {
                        showToast('Terjadi kesalahan: Response tidak valid', 'error');
                    }
                } else {
                    if (response.status === 401) {
                        window.location.href = '/login';
                        return;
                    }
                    showToast('Gagal memperbarui link', 'error');
                }
            } catch (error) {
                console.error('Error updating link:', error);
                showToast('Terjadi kesalahan jaringan', 'error');
            }
        },

        async toggleLinkStatus(link) {
            try {
                const response = await fetch(`/api/links/${link.id}/toggle`, {
                    method: 'PATCH',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        showToast(data.message, 'success');
                        this.loadLinks();
                    } else {
                        showToast('Terjadi kesalahan: Response tidak valid', 'error');
                    }
                } else {
                    if (response.status === 401) {
                        window.location.href = '/login';
                        return;
                    }
                    showToast('Gagal mengubah status link', 'error');
                }
            } catch (error) {
                console.error('Error toggling link status:', error);
                showToast('Terjadi kesalahan jaringan', 'error');
            }
        },

        async deleteLink(link) {
            if (confirm(`Apakah Anda yakin ingin menghapus link "${link.short_url}"?`)) {
                try {
                    const response = await fetch(`/api/links/${link.id}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            showToast('Link berhasil dihapus!', 'success');
                            this.loadLinks();
                        } else {
                            showToast('Link berhasil dihapus!', 'success');
                            this.loadLinks();
                        }
                    } else {
                        if (response.status === 401) {
                            window.location.href = '/login';
                            return;
                        }
                        showToast('Gagal menghapus link', 'error');
                    }
                } catch (error) {
                    console.error('Error deleting link:', error);
                    showToast('Terjadi kesalahan jaringan', 'error');
                }
            }
        }
    }));
});

// Global functions
async function refreshLinks() {
    const shortlinkData = Alpine.$data(document.querySelector('[x-data*="shortlinkData"]'));
    if (shortlinkData) {
        await shortlinkData.loadLinks();
        showToast('Data berhasil diperbarui!', 'success');
    }
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white max-w-sm transform transition-all duration-300 translate-x-full`;

    const colors = {
        success: 'bg-green-600',
        error: 'bg-red-600',
        warning: 'bg-yellow-600',
        info: 'bg-blue-600'
    };

    toast.classList.add(colors[type] || colors.info);
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => toast.classList.remove('translate-x-full'), 100);
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
}
</script>