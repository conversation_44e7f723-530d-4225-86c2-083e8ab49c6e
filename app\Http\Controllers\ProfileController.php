<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class ProfileController extends Controller
{
    public function uploadProfilePhoto(Request $request)
    {
        try {
            $request->validate([
                'photo' => 'required|image|mimes:jpg,jpeg,png,gif|max:2048',
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'errors' => $e->errors(),
            ], 422);
        }

        $user = Auth::user();
        $file = $request->file('photo');
        $filename = 'user_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();

        // Create images directory if not exists
        $imagesPath = public_path('images');
        if (!file_exists($imagesPath)) {
            mkdir($imagesPath, 0755, true);
        }

        $file->move($imagesPath, $filename);
        $publicPath = '/images/' . $filename;

        // Optional: hapus foto lama jika ada
        if ($user->profile_photo) {
            $oldPhotoPath = public_path(ltrim($user->profile_photo, '/'));
            if (file_exists($oldPhotoPath)) {
                @unlink($oldPhotoPath);
            }
        }

        $user->profile_photo = $publicPath;
        $user->save();

        return response()->json([
            'success' => true,
            'photo_url' => $publicPath,
        ]);
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $user->id,
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'bio' => 'nullable|string',
        ]);
        $user->update($validated);
        return response()->json([
            'success' => true,
            'message' => 'Profil berhasil diperbarui',
            'user' => $user,
        ]);
    }

    public function changePassword(Request $request)
    {
        try {
            $request->validate([
                'old_password' => 'required',
                'new_password' => 'required|min:8|confirmed',
                'new_password_confirmation' => 'required',
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $e->errors(),
            ], 422);
        }

        $user = Auth::user();

        // Check if old password is correct
        if (!Hash::check($request->old_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Password lama tidak benar',
            ], 422);
        }

        // Check if new password is different from old password
        if (Hash::check($request->new_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Password baru harus berbeda dari password lama',
            ], 422);
        }

        // Update password
        $user->password = Hash::make($request->new_password);
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Password berhasil diubah',
        ]);
    }

    public function updateNotificationSettings(Request $request)
    {
        $user = Auth::user();

        // Get current notification settings or create default
        $settings = $user->notification_settings ?? [
            'email_link_created' => true,
            'email_earnings' => true,
            'email_frequency' => 'instant',
            'push_link_clicks' => false,
            'push_earning_milestone' => true,
            'billing_reminder' => true,
            'billing_invoice' => true,
        ];

        // Update with new settings
        foreach ($request->all() as $key => $value) {
            if (in_array($key, [
                'email_link_created', 'email_earnings', 'email_frequency',
                'push_link_clicks', 'push_earning_milestone',
                'billing_reminder', 'billing_invoice'
            ])) {
                $settings[$key] = $value;
            }
        }

        // Save to user
        $user->notification_settings = $settings;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Pengaturan notifikasi berhasil diperbarui',
            'settings' => $settings,
        ]);
    }

    public function updateBillingSettings(Request $request)
    {
        $user = Auth::user();

        // Get current billing settings or create default
        $settings = $user->billing_settings ?? [
            'auto_renewal' => true,
            'payment_method' => null,
            'billing_address' => null,
        ];

        // Update with new settings
        foreach ($request->all() as $key => $value) {
            if (in_array($key, ['auto_renewal', 'payment_method', 'billing_address'])) {
                $settings[$key] = $value;
            }
        }

        // Save to user
        $user->billing_settings = $settings;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Pengaturan billing berhasil diperbarui',
            'settings' => $settings,
        ]);
    }
}
