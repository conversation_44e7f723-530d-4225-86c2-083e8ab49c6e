<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar - sub4short+</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <style>
        body { background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%); }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-blue-800 to-blue-600 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-all duration-300">
    <div class="bg-white/90 dark:bg-gray-900/90 rounded-2xl shadow-2xl p-8 w-full max-w-md backdrop-blur-md">
        <div class="flex flex-col items-center mb-6">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full flex items-center justify-center mb-2 shadow-lg">
                <i class="fa-solid fa-user-plus text-white text-3xl"></i>
            </div>
            <h1 class="text-2xl font-bold mb-2 text-gray-900 dark:text-white">Daftar ke <span class="text-blue-600">sub4short+</span></h1>
            <p class="text-gray-500 dark:text-gray-300 text-sm">Buat akun baru untuk mulai menggunakan layanan</p>
        </div>
        <form action="{{ route('register.attempt') }}" method="POST" class="flex flex-col gap-5 mt-2" x-data="{ loading: false }" @submit.prevent="loading = true; $el.submit();">
            @csrf
            <div>
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Nama Lengkap</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-600">
                        <i class="fa-solid fa-user"></i>
                    </span>
                    <input type="text" name="name" required class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="Nama Lengkap" value="{{ old('name') }}">
                </div>
            </div>
            <div>
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Username</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-600">
                        <i class="fa-solid fa-at"></i>
                    </span>
                    <input type="text" name="username" required class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="Username" value="{{ old('username') }}">
                </div>
            </div>
            <div>
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Email</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-600">
                        <i class="fa-regular fa-envelope"></i>
                    </span>
                    <input type="email" name="email" required class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="<EMAIL>" value="{{ old('email') }}">
                </div>
            </div>
            <div x-data="{ show: false, password: '' }">
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Password</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-600">
                        <i class="fa-solid fa-lock"></i>
                    </span>
                    <input :type="show ? 'text' : 'password'" name="password" required class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="Password" x-model="password">
                    <button type="button" @click="show = !show" class="absolute right-3 top-1/2 -translate-y-1/2 focus:outline-none">
                        <template x-if="show">
                            <i class="fa-regular fa-eye-slash text-blue-600"></i>
                        </template>
                        <template x-if="!show">
                            <i class="fa-regular fa-eye text-blue-600"></i>
                        </template>
                    </button>
                </div>
                <div class="mt-2">
                    <template x-if="password.length">
                        <div>
                            <div class="w-full h-2 rounded bg-gray-200 dark:bg-gray-700 overflow-hidden">
                                <div :class="{
                                    'bg-red-500': strength === 'lemah',
                                    'bg-yellow-400': strength === 'sedang',
                                    'bg-green-500': strength === 'kuat'
                                }" class="h-2 transition-all duration-300" :style="'width: ' + barWidth + '%'">
                                </div>
                            </div>
                            <div class="mt-1 text-xs font-semibold" :class="{
                                'text-red-500': strength === 'lemah',
                                'text-yellow-500': strength === 'sedang',
                                'text-green-600': strength === 'kuat'
                            }" x-text="label"></div>
                        </div>
                    </template>
                </div>
            </div>
            <div>
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Konfirmasi Password</label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-600">
                        <i class="fa-solid fa-lock"></i>
                    </span>
                    <input type="password" name="password_confirmation" required class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="Konfirmasi Password">
                </div>
            </div>
            <div>
                <label class="block text-sm font-semibold mb-1 text-gray-700 dark:text-gray-300">Kode Referal <span class='text-gray-400'>(opsional)</span></label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-400">
                        <i class="fa-solid fa-gift"></i>
                    </span>
                    <input type="text" name="referral_code" class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white transition-all" placeholder="Masukkan kode referal (jika ada)" value="{{ old('referral_code') }}">
                </div>
            </div>
            @if($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded text-sm flex items-center gap-2 animate-pulse">
                    <i class="fa-solid fa-circle-exclamation"></i>
                    {{ $errors->first() }}
                </div>
            @endif
            <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-700 text-white py-2 rounded font-semibold shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95 flex items-center justify-center gap-2 relative" :disabled="loading">
                <template x-if="loading">
                    <span class="absolute left-3">
                        <span class="w-5 h-5 border-2 border-white border-t-transparent border-solid rounded-full animate-spin"></span>
                    </span>
                </template>
                <i class="fa-solid fa-user-plus" :class="loading ? 'opacity-50' : ''"></i>
                <span :class="loading ? 'opacity-50' : ''">Daftar</span>
            </button>
        </form>
        <div class="mt-6 text-center text-sm text-gray-600 dark:text-gray-300">
            Sudah punya akun? <a href="{{ url('/auth/login') }}" class="text-blue-600 hover:underline font-semibold">Masuk</a>
        </div>
    </div>
    @if(request('success'))
    <div x-data="{ show: true }" x-show="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl p-8 max-w-xs w-full flex flex-col items-center animate-bounceIn">
            <div class="mb-4">
                <div class="w-16 h-16 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                    <svg class="w-12 h-12 text-green-500 animate-ping-slow" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" class="opacity-20"></circle>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M7 13l3 3 7-7" class="animate-checkmark"></path>
                    </svg>
                </div>
            </div>
            <h2 class="text-xl font-bold text-green-700 dark:text-green-300 mb-2">Berhasil!</h2>
            <p class="text-gray-700 dark:text-gray-200 text-center mb-2">Silakan login menggunakan akun anda untuk menuju ke halaman utama.</p>
            <button @click="show = false; window.location.href='{{ url('/auth/login') }}'" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition">Login Sekarang</button>
        </div>
        <script>
        setTimeout(function() {
            window.location.href = '{{ url('/auth/login') }}';
        }, 2500);
        </script>
        <style>
        .animate-bounceIn {
            animation: bounceIn .7s cubic-bezier(.68,-0.55,.27,1.55);
        }
        @keyframes bounceIn {
            0% { transform: scale(0.7); opacity: 0; }
            60% { transform: scale(1.1); opacity: 1; }
            80% { transform: scale(0.95); }
            100% { transform: scale(1); }
        }
        .animate-ping-slow {
            animation: ping-slow 1.2s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
        @keyframes ping-slow {
            75%, 100% { transform: scale(1.2); opacity: 0; }
        }
        .animate-checkmark {
            stroke-dasharray: 24;
            stroke-dashoffset: 24;
            animation: checkmark 0.7s 0.2s cubic-bezier(.68,-0.55,.27,1.55) forwards;
        }
        @keyframes checkmark {
            to { stroke-dashoffset: 0; }
        }
        </style>
    </div>
    @endif
    <script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('passwordStrength', () => ({
            password: '',
            get strength() {
                if (this.password.length < 6) return 'lemah';
                if (this.password.match(/[A-Z]/) && this.password.match(/[0-9]/) && this.password.match(/[^A-Za-z0-9]/) && this.password.length >= 8) return 'kuat';
                if (this.password.length >= 6) return 'sedang';
                return 'lemah';
            },
            get barWidth() {
                if (this.strength === 'lemah') return 33;
                if (this.strength === 'sedang') return 66;
                if (this.strength === 'kuat') return 100;
                return 0;
            },
            get label() {
                if (this.strength === 'lemah') return 'Lemah';
                if (this.strength === 'sedang') return 'Sedang';
                if (this.strength === 'kuat') return 'Kuat';
                return '';
            }
        }));
    });
    </script>
</body>
</html> 