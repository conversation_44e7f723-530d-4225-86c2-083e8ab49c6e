<!-- Pay Page (Pembayaran) -->
<div class="max-w-xl mx-auto py-10 px-4">
    <div class="bg-white/90 dark:bg-gray-900/90 rounded-2xl p-8 border border-blue-200 dark:border-blue-700 shadow-2xl backdrop-blur-md">
        <div class="flex items-center mb-8">
            <div class="w-14 h-14 bg-gradient-to-br from-green-400 to-blue-500 dark:from-green-700 dark:to-blue-700 rounded-xl flex items-center justify-center mr-5 shadow-lg">
                <i class="fa-solid fa-credit-card text-white text-3xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-extrabold text-gray-900 dark:text-white tracking-tight">Pembayaran</h2>
                <p class="text-base text-gray-600 dark:text-gray-300 mt-1">Selesaikan pembayaran untuk mengaktifkan {{ $plan ?? 'Platinum Plan' }}.</p>
            </div>
        </div>
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center"><i class="fa-solid fa-receipt text-blue-500 mr-2"></i> Ringkasan Pesanan</h3>
            <div class="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/20 rounded-xl p-5 border border-blue-100 dark:border-blue-800 mb-4">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-700 dark:text-gray-300">{{ $plan ?? 'Platinum Plan' }}</span>
                    <span class="font-bold text-blue-600 dark:text-blue-400">Rp {{ number_format($harga ?? 50000, 0, ',', '.') }}</span>
                </div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-700 dark:text-gray-300">Pajak</span>
                    <span class="font-semibold text-yellow-600 dark:text-yellow-300">Rp {{ number_format($pajak ?? 10000, 0, ',', '.') }}</span>
                </div>
                <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-700 dark:text-gray-300">Fee</span>
                    <span class="font-semibold text-pink-600 dark:text-pink-300">Rp {{ number_format($fee ?? 5000, 0, ',', '.') }}</span>
                </div>
                <hr class="my-2 border-gray-300 dark:border-gray-600">
                <div class="flex justify-between items-center text-lg font-extrabold">
                    <span class="text-gray-900 dark:text-white">Total</span>
                    <span class="text-green-600 dark:text-green-400">Rp {{ number_format($total ?? 65000, 0, ',', '.') }}</span>
                </div>
            </div>
        </div>
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center"><i class="fa-solid fa-money-bill-wave text-green-500 mr-2"></i> Instruksi Pembayaran</h3>
            <ol class="list-decimal pl-6 text-gray-700 dark:text-gray-300 space-y-2 text-base">
                <li>Pilih metode pembayaran di bawah ini.</li>
                <li>Lakukan pembayaran sesuai nominal total.</li>
                <li>Setelah pembayaran, klik tombol "Konfirmasi Pembayaran".</li>
            </ol>
            <div class="mt-5">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Metode Pembayaran</label>
                <select class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200">
                    <option>Transfer Bank</option>
                    <option>QRIS</option>
                    <option>E-Wallet (OVO, DANA, GoPay)</option>
                </select>
            </div>
        </div>
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <button class="w-full md:w-auto bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white py-3 px-6 rounded-lg text-lg font-bold shadow-lg transition-all duration-200 transform hover:scale-105 active:scale-95">
                <i class="fa-solid fa-check-circle mr-2"></i>
                Konfirmasi Pembayaran
            </button>
            <span class="text-xs text-gray-500 dark:text-gray-400 mt-2 md:mt-0">Status: <span class="font-semibold text-yellow-600 dark:text-yellow-300">Menunggu Pembayaran</span></span>
        </div>
    </div>
</div> 