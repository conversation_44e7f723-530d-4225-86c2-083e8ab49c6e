<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Auth\GoogleController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

Route::get('/', function () {
    return view('landing');
});

Route::get('/auth/login', function () {
    return view('auth.login');
})->name('login');

Route::get('/auth/register', function () {
    return view('auth.register');
})->name('register');

Route::get('/auth/forgot-password', function () {
    return view('auth.forgot-password');
})->name('forgot-password');

// Google Authentication Routes
Route::get('/auth/google', [GoogleController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/google/callback', [GoogleController::class, 'handleGoogleCallback'])->name('auth.google.callback');

// Bronze Plan Routes
Route::get('/bronze/dashboard', function () {
    return view('bronze-plan.dashboard', ['activeTab' => 'dashboard']);
})->name('bronze.dashboard');

Route::get('/bronze/shortlink', function () {
    return view('bronze-plan.dashboard', ['activeTab' => 'shortlink']);
})->name('bronze.shortlink');

Route::get('/bronze/analytics', function () {
    return view('bronze-plan.dashboard', ['activeTab' => 'analytics']);
})->name('bronze.analytics');

Route::get('/bronze/withdraw', function () {
    return view('bronze-plan.dashboard', ['activeTab' => 'withdraw']);
})->name('bronze.withdraw');

Route::get('/bronze/settings', function () {
    return view('bronze-plan.dashboard', ['activeTab' => 'settings']);
})->name('bronze.settings');

Route::get('/bronze/upgrade-plan', function () {
    return view('pages.upgrade-plan');
})->name('bronze.upgrade-plan');

Route::get('/bronze/payment-process', function () {
    return view('pages.process.payment-process');
})->name('bronze.payment-process');

Route::get('/bronze/pay', function () {
    $harga = 50000;
    $pajak = 10000;
    $fee = 5000;
    $total = $harga + $pajak + $fee;
    return view('pages.pay', [
        'plan' => 'Platinum Plan',
        'harga' => $harga,
        'pajak' => $pajak,
        'fee' => $fee,
        'total' => $total,
    ]);
})->name('bronze.pay');

Route::post('/profile/upload-photo', [ProfileController::class, 'uploadProfilePhoto'])->middleware('auth')->name('profile.upload-photo');

Route::post('/profile/update', [\App\Http\Controllers\ProfileController::class, 'updateProfile'])->middleware('auth')->name('profile.update');

Route::post('/profile/change-password', [ProfileController::class, 'changePassword'])->middleware('auth')->name('profile.change-password');

Route::post('/profile/notification-settings', [ProfileController::class, 'updateNotificationSettings'])->middleware('auth')->name('profile.update-notification-settings');

Route::post('/profile/billing-settings', [ProfileController::class, 'updateBillingSettings'])->middleware('auth')->name('profile.update-billing-settings');

Route::post('/auth/login', function (Request $request) {
    $credentials = $request->only('email', 'password');
    if (Auth::attempt($credentials, $request->has('remember'))) {
        $request->session()->regenerate();
        return redirect()->intended('/bronze/dashboard');
    }
    return back()->withErrors(['email' => 'Email atau password salah'])->withInput();
})->name('login.attempt');

Route::post('/auth/register', function (Request $request) {
    $request->validate([
        'name' => 'required|string|max:255',
        'username' => 'required|string|max:255|unique:users',
        'email' => 'required|string|email|max:255|unique:users',
        'password' => 'required|string|min:6|confirmed',
    ]);
    $user = \App\Models\User::create([
        'name' => $request->name,
        'username' => $request->username,
        'email' => $request->email,
        'password' => bcrypt($request->password),
        'referral_code' => $request->referral_code,
    ]);
    return redirect('/auth/register?success=1');
})->name('register.attempt');
