<!-- Settings Container with Alpine.js data -->
<div x-data="{
    twoFAEnabled: false,
    notifEmailEnabled: true,
    notifPushEnabled: false,
    notifBillingEnabled: true
}">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Pengaturan</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Ke<PERSON>la profil dan preferensi akun Anda</p>
        </div>
    </div>

<!-- Settings Navigation -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-0 mb-8 transition-all duration-300">
    <div class="flex overflow-x-auto no-scrollbar border-b border-gray-200 dark:border-gray-700 px-2 md:px-6 gap-1 md:gap-2">
        <button id="tabProfile" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="profile">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-sm">Profil</span>
        </button>
        <button id="tabSecurity" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="security">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span class="text-sm">Keamanan</span>
        </button>
        <button id="tabNotifications" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="notifications">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5V13"></path>
            </svg>
            <span class="text-sm">Notifikasi</span>
        </button>
        <button id="tabBilling" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="billing">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="text-sm">Billing</span>
        </button>
        <button id="tabLiveChat" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="livechat">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <span class="text-sm">Live Chat</span>
        </button>
    </div>
</div>

<!-- Profile Settings -->
<div class="space-y-8">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-100 dark:border-gray-700 p-4 md:p-6 transition-all duration-300 mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            Informasi Profil
        </h2>
        <form id="profileForm" class="space-y-6">
            <div class="flex items-center space-x-4 md:space-x-6">
                <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center overflow-hidden relative">
                    <img id="profilePhotoPreview" src="<?php echo e(optional(Auth::user())->profile_photo ? asset(optional(Auth::user())->profile_photo) : ''); ?>" alt="Foto Profil" class="w-full h-full object-cover rounded-full" style="display: <?php echo e(optional(Auth::user())->profile_photo ? 'block' : 'none'); ?>;">
                    <span id="profilePhotoInitial" class="text-white text-xl md:text-2xl font-bold" style="display: <?php echo e(optional(Auth::user())->profile_photo ? 'none' : 'block'); ?>;">U</span>
                    <div id="photoUploadingSpinner" class="absolute inset-0 flex flex-col items-center justify-center rounded-full bg-black/50 backdrop-blur-sm" style="display:none;">
                        <div class="w-8 h-8 border-3 border-white/30 border-t-white border-solid rounded-full animate-spin mb-2"></div>
                        <span class="text-white text-xs font-medium">Uploading...</span>
                    </div>
                    <div id="photoProgressBarContainer" class="absolute bottom-0 left-0 right-0 h-2 bg-gray-200 dark:bg-gray-700 rounded-b-full overflow-hidden" style="display:none;">
                        <div id="photoProgressBar" class="h-2 bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-300 ease-out relative progress-shine" style="width:0%">
                            <div class="absolute inset-0 bg-white/20 animate-pulse"></div>
                        </div>
                    </div>
                </div>
                <div>
                    <input type="file" id="profile-photo-input" class="hidden" accept="image/*">
                    <button type="button" id="changePhotoBtn" class="bg-blue-600 dark:bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i id="photoBtnIcon" class="fa-solid fa-camera"></i>
                        <span id="photoBtnText">Ganti Foto</span>
                    </button>
                    <input type="file" id="fileInput" class="hidden" accept="image/*">
                    <p class="text-xs md:text-sm text-gray-500 dark:text-gray-400 mt-1">JPG, PNG atau GIF. Maksimal 2MB.</p>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div>
                    <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nama Lengkap</label>
                    <input type="text" id="name" value="<?php echo e(optional(Auth::user())->name ?: 'Nama tidak ditemukan'); ?>" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 <?php echo e(optional(Auth::user())->name ? '' : 'italic text-red-500 dark:text-red-400'); ?>">
                </div>
                <div>
                    <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Username</label>
                    <input type="text" id="username" value="<?php echo e(optional(Auth::user())->username); ?>" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400">
                </div>
            </div>
            <div>
                <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                <input type="email" id="email" value="<?php echo e(optional(Auth::user())->email ?: 'Email tidak ditemukan'); ?>" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 <?php echo e(optional(Auth::user())->email ? '' : 'italic text-red-500 dark:text-red-400'); ?>">
            </div>
            <div>
                <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bio</label>
                <textarea rows="3" id="bio" placeholder="Ceritakan sedikit tentang diri Anda..." class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 resize-none"><?php echo e(optional(Auth::user())->bio); ?></textarea>
            </div>
            <div class="flex justify-end sticky bottom-0 bg-white dark:bg-gray-800 py-2 z-10 md:static md:bg-transparent">
                <button type="submit" class="w-full md:w-auto bg-blue-600 dark:bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 font-semibold flex items-center justify-center gap-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                    </svg>
                    Simpan Perubahan
                </button>
            </div>
        </form>
        <!-- Toast Notification -->
        <div id="profileToast" class="fixed z-50 right-4 bottom-4 md:right-8 md:bottom-8 px-4 py-3 rounded-lg shadow-lg text-white hidden min-w-[220px] max-w-[90vw]"></div>
    </div>
</div>

<!-- Security Settings -->
<div id="tabContentSecurity" class="hidden space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Keamanan Akun</h2>
                <p class="text-gray-600 dark:text-gray-300">Lindungi akun Anda dengan fitur keamanan terdepan</p>
            </div>
        </div>

        <!-- Security Status -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Password</p>
                        <p class="text-xs text-green-600 dark:text-green-400">Kuat</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">2FA</p>
                        <p class="text-xs text-yellow-600 dark:text-yellow-400">Belum Aktif</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Sesi Aktif</p>
                        <p class="text-xs text-blue-600 dark:text-blue-400">2 Perangkat</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Section -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Ganti Password</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Perbarui password Anda secara berkala untuk keamanan optimal</p>
            </div>
        </div>
        <!-- Change Password Form -->
        <form id="changePasswordForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password Lama</label>
                    <div class="relative">
                        <input type="password" id="oldPassword" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 pr-10" placeholder="Masukkan password lama" required>
                        <button type="button" onclick="togglePasswordVisibility('oldPassword')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password Baru</label>
                    <div class="relative">
                        <input type="password" id="newPassword" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 pr-10" placeholder="Password baru" required>
                        <button type="button" onclick="togglePasswordVisibility('newPassword')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                    <!-- Password Strength Indicator -->
                    <div class="mt-2">
                        <div class="flex items-center gap-2">
                            <div class="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                <div id="passwordStrengthBar" class="h-full transition-all duration-300 rounded-full" style="width: 0%"></div>
                            </div>
                            <span id="passwordStrengthText" class="text-xs font-medium text-gray-500 dark:text-gray-400">Lemah</span>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Konfirmasi Password</label>
                    <div class="relative">
                        <input type="password" id="confirmPassword" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 pr-10" placeholder="Ulangi password baru" required>
                        <button type="button" onclick="togglePasswordVisibility('confirmPassword')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="passwordMatchIndicator" class="mt-2 text-xs hidden">
                        <span class="flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Password cocok
                        </span>
                    </div>
                </div>
            </div>

            <!-- Password Requirements -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Persyaratan Password:</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div id="req-length" class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Minimal 8 karakter
                    </div>
                    <div id="req-uppercase" class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Huruf besar (A-Z)
                    </div>
                    <div id="req-lowercase" class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Huruf kecil (a-z)
                    </div>
                    <div id="req-number" class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Angka (0-9)
                    </div>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                <button type="button" onclick="generateStrongPassword()" class="px-4 py-2 text-blue-600 dark:text-blue-400 border border-blue-600 dark:border-blue-400 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 font-medium">
                    Generate Password Kuat
                </button>
                <button type="submit" id="changePasswordBtn" class="px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 font-medium flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span id="changePasswordBtnText">Ganti Password</span>
                    <svg id="changePasswordSpinner" class="hidden animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                </button>
            </div>
            <div id="changePasswordFeedback" class="hidden p-4 rounded-lg"></div>
        </form>
    </div>

    <!-- Two-Factor Authentication -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Two-Factor Authentication (2FA)</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Tambahkan lapisan keamanan ekstra dengan autentikasi dua faktor</p>
            </div>
        </div>

        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div class="flex items-start gap-3">
                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div>
                    <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">2FA Belum Aktif</h4>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">Akun Anda lebih rentan terhadap serangan. Aktifkan 2FA untuk perlindungan maksimal.</p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 transition-colors">
                <div class="flex items-center gap-3 mb-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h4 class="font-medium text-gray-900 dark:text-white">Aplikasi Authenticator</h4>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Gunakan Google Authenticator, Authy, atau aplikasi serupa</p>
                <button onclick="setup2FA('app')" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    Setup Authenticator
                </button>
            </div>

            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-green-300 dark:hover:border-green-600 transition-colors">
                <div class="flex items-center gap-3 mb-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h4 class="font-medium text-gray-900 dark:text-white">SMS Verification</h4>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">Terima kode verifikasi melalui SMS</p>
                <button onclick="setup2FA('sms')" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium">
                    Setup SMS
                </button>
            </div>
        </div>
    </div>

    <!-- Active Sessions -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sesi Aktif</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Kelola perangkat yang terhubung ke akun Anda</p>
                </div>
            </div>
            <button onclick="logoutAllSessions()" class="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 font-medium text-sm">
                Logout Semua
            </button>
        </div>

        <div class="space-y-4" id="activeSessions">
            <!-- Current Session -->
            <div class="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div class="flex items-center gap-4">
                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center gap-2">
                            <span class="font-medium text-gray-900 dark:text-white">Windows - Chrome</span>
                            <span class="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs px-2 py-1 rounded-full font-medium">Sesi Ini</span>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300">Jakarta, Indonesia</div>
                        <div class="text-xs text-green-600 dark:text-green-400">Aktif sekarang • IP: *************</div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500 dark:text-gray-400">Login terakhir</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500">Hari ini, 14:30</div>
                </div>
            </div>

            <!-- Other Sessions -->
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div class="flex items-center gap-4">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900 dark:text-white">iPhone - Safari</div>
                        <div class="text-sm text-gray-600 dark:text-gray-300">Jakarta, Indonesia</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">2 jam yang lalu • IP: *************</div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="text-right">
                        <div class="text-sm text-gray-500 dark:text-gray-400">Login terakhir</div>
                        <div class="text-xs text-gray-400 dark:text-gray-500">Hari ini, 12:15</div>
                    </div>
                    <button onclick="logoutSession('session-2')" class="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-sm font-medium px-3 py-1 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </div>
        <!-- Security Tips -->
        <div class="mt-8">
            <div class="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2"><i class="fa-solid fa-lightbulb text-yellow-400"></i> Tips Keamanan</div>
            <ul class="list-disc pl-6 text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>Gunakan password unik dan kuat.</li>
                <li>Jangan bagikan password ke siapa pun.</li>
                <li>Aktifkan 2FA untuk perlindungan ekstra.</li>
                <li>Logout dari perangkat yang tidak dikenal.</li>
            </ul>
        </div>
    </div>
</div>

<!-- Notifications Settings -->
<div id="tabContentNotifications" class="hidden space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl p-6 border border-purple-100 dark:border-purple-800">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5V13"></path>
                </svg>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Pengaturan Notifikasi</h2>
                <p class="text-gray-600 dark:text-gray-300">Kelola preferensi notifikasi dan tetap terinformasi</p>
            </div>
        </div>

        <!-- Notification Status Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Email</p>
                        <p id="emailStatus" class="text-xs text-green-600 dark:text-green-400">Aktif</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5V13"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Push</p>
                        <p id="pushStatus" class="text-xs text-red-600 dark:text-red-400">Nonaktif</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Billing</p>
                        <p id="billingStatus" class="text-xs text-green-600 dark:text-green-400">Aktif</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Notifications -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notifikasi Email</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Terima notifikasi penting melalui email</p>
            </div>
        </div>

        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Link Berhasil Dibuat</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Notifikasi saat shortlink baru berhasil dibuat</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Terakhir dikirim: 2 jam yang lalu</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="emailLinkCreated" class="sr-only peer" checked onchange="updateNotificationSetting('email_link_created', this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Laporan Pendapatan</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Ringkasan pendapatan harian dan mingguan</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Dikirim setiap hari pukul 18:00</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="emailEarnings" class="sr-only peer" checked onchange="updateNotificationSetting('email_earnings', this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Peringatan Keamanan</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Login dari perangkat baru atau aktivitas mencurigakan</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Tidak dapat dinonaktifkan untuk keamanan</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer opacity-50">
                    <input type="checkbox" class="sr-only peer" checked disabled>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>
        </div>

        <!-- Email Frequency Settings -->
        <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 class="font-medium text-blue-900 dark:text-blue-200 mb-3">Frekuensi Email</h4>
            <div class="space-y-2">
                <label class="flex items-center">
                    <input type="radio" name="emailFrequency" value="instant" class="text-blue-600 focus:ring-blue-500" checked onchange="updateEmailFrequency(this.value)">
                    <span class="ml-2 text-sm text-blue-800 dark:text-blue-200">Langsung (Real-time)</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="emailFrequency" value="daily" class="text-blue-600 focus:ring-blue-500" onchange="updateEmailFrequency(this.value)">
                    <span class="ml-2 text-sm text-blue-800 dark:text-blue-200">Ringkasan Harian (18:00)</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="emailFrequency" value="weekly" class="text-blue-600 focus:ring-blue-500" onchange="updateEmailFrequency(this.value)">
                    <span class="ml-2 text-sm text-blue-800 dark:text-blue-200">Ringkasan Mingguan (Senin)</span>
                </label>
            </div>
        </div>
    </div>

    <!-- Push Notifications -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5V13"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Push Notifications</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Notifikasi langsung di browser Anda</p>
            </div>
        </div>

        <!-- Push Permission Status -->
        <div id="pushPermissionStatus" class="mb-6 p-4 rounded-lg border">
            <!-- Will be populated by JavaScript -->
        </div>

        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Link Clicks</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Notifikasi saat ada yang mengklik link Anda</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="pushLinkClicks" class="sr-only peer" onchange="updateNotificationSetting('push_link_clicks', this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
                </label>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Milestone Pendapatan</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Notifikasi saat mencapai target pendapatan</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="pushEarningMilestone" class="sr-only peer" checked onchange="updateNotificationSetting('push_earning_milestone', this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
                </label>
            </div>
        </div>

        <!-- Test Push Notification -->
        <div class="mt-6 flex gap-3">
            <button onclick="testPushNotification()" class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium">
                Test Push Notification
            </button>
            <button onclick="requestPushPermission()" id="enablePushBtn" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                Aktifkan Push Notifications
            </button>
        </div>
    </div>

    <!-- Billing & Transaction Notifications -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Billing & Transaksi</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Notifikasi terkait pembayaran dan transaksi</p>
            </div>
        </div>

        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Pembayaran Berhasil</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Konfirmasi saat pembayaran berhasil diproses</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Tidak dapat dinonaktifkan</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer opacity-50">
                    <input type="checkbox" class="sr-only peer" checked disabled>
                    <div class="w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-yellow-600"></div>
                </label>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Pengingat Pembayaran</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Pengingat sebelum tanggal jatuh tempo</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">3 hari sebelum jatuh tempo</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="billingReminder" class="sr-only peer" checked onchange="updateNotificationSetting('billing_reminder', this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-yellow-600"></div>
                </label>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mt-1">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Invoice & Receipt</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300">Terima invoice dan receipt via email</p>
                    </div>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="billingInvoice" class="sr-only peer" checked onchange="updateNotificationSetting('billing_invoice', this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-yellow-600"></div>
                </label>
            </div>
        </div>
    </div>
</div>

<!-- Billing Settings -->
<div id="tabContentBilling" class="hidden space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-2xl p-6 border border-amber-100 dark:border-amber-800">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Billing & Langganan</h2>
                <p class="text-gray-600 dark:text-gray-300">Kelola pembayaran dan upgrade plan Anda</p>
            </div>
        </div>

        <!-- Billing Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Status</p>
                        <p class="text-xs text-green-600 dark:text-green-400">Aktif</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Total Spent</p>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Rp 200.000</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Next Payment</p>
                        <p class="text-xs text-purple-600 dark:text-purple-400">31 Des 2024</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Plan -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Plan Saat Ini</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Informasi langganan aktif Anda</p>
            </div>
        </div>

        <div class="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl p-6 border border-amber-200 dark:border-amber-800">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div class="flex items-center gap-4">
                    <div class="w-16 h-16 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-xl flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center gap-3 mb-2">
                            <h4 class="text-xl font-bold text-gray-900 dark:text-white">Bronze Plan</h4>
                            <span class="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs px-3 py-1 rounded-full font-medium">Aktif</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">Rp 50.000/bulan • Diperpanjang otomatis</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-md border border-gray-200 dark:border-gray-600">100 Links</span>
                            <span class="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-md border border-gray-200 dark:border-gray-600">Email Support</span>
                            <span class="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-md border border-gray-200 dark:border-gray-600">Basic Analytics</span>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <div class="text-center sm:text-right">
                        <p class="text-sm text-gray-500 dark:text-gray-400">Berlaku hingga</p>
                        <p class="text-lg font-semibold text-amber-600 dark:text-amber-400">31 Des 2024</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">23 hari lagi</p>
                    </div>
                    <div class="flex flex-col gap-2">
                        <button onclick="upgradePlan()" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            Upgrade Plan
                        </button>
                        <button onclick="manageBilling()" class="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 py-2 px-4 rounded-lg font-medium transition-all duration-200">
                            Kelola Billing
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Stats -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Links Created</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">67/100</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: 67%"></div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Clicks This Month</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">1,234</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full" style="width: 82%"></div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Earnings</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Rp 45.600</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div class="bg-yellow-600 h-2 rounded-full" style="width: 76%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Metode Pembayaran</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Kelola kartu kredit dan metode pembayaran</p>
                </div>
            </div>
            <button onclick="addPaymentMethod()" class="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Tambah
            </button>
        </div>

        <div class="space-y-4" id="paymentMethods">
            <!-- Primary Payment Method -->
            <div class="border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-8 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M1.5 8.67v8.58a3 3 0 003 3h15a3 3 0 003-3V8.67l-8.928 5.493a3 3 0 01-3.144 0L1.5 8.67z"/>
                                <path d="M22.5 6.908V6.75a3 3 0 00-3-3h-15a3 3 0 00-3 3v.158l9.714 5.978a1.5 1.5 0 001.572 0L22.5 6.908z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center gap-2">
                                <span class="font-medium text-gray-900 dark:text-white">•••• •••• •••• 4242</span>
                                <span class="bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 text-xs px-2 py-1 rounded-full font-medium">Primary</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-300">Visa • Expires 12/26</p>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="editPaymentMethod('card-1')" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button onclick="deletePaymentMethod('card-1')" class="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Alternative Payment Methods -->
            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-8 bg-gradient-to-r from-green-600 to-green-700 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xs">DANA</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-900 dark:text-white">Dana Wallet</span>
                            <p class="text-sm text-gray-600 dark:text-gray-300">+62 812-3456-7890</p>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="setPrimaryPayment('dana-1')" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
                            Set Primary
                        </button>
                        <button onclick="deletePaymentMethod('dana-1')" class="text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Auto-renewal Settings -->
        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">Perpanjangan Otomatis</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Langganan akan diperpanjang otomatis setiap bulan</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="autoRenewal" class="sr-only peer" checked onchange="toggleAutoRenewal(this.checked)">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Riwayat Transaksi</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Semua pembayaran dan transaksi Anda</p>
                </div>
            </div>
            <div class="flex gap-2">
                <button onclick="exportTransactions()" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </button>
                <button onclick="filterTransactions()" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Transaction Summary -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-blue-900 dark:text-blue-200">Total Spent</p>
                        <p class="text-lg font-bold text-blue-600 dark:text-blue-400">Rp 200.000</p>
                    </div>
                </div>
            </div>
            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-green-900 dark:text-green-200">Successful</p>
                        <p class="text-lg font-bold text-green-600 dark:text-green-400">4</p>
                    </div>
                </div>
            </div>
            <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-yellow-900 dark:text-yellow-200">Pending</p>
                        <p class="text-lg font-bold text-yellow-600 dark:text-yellow-400">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-red-900 dark:text-red-200">Failed</p>
                        <p class="text-lg font-bold text-red-600 dark:text-red-400">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Tanggal</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Deskripsi</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Metode</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Jumlah</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Status</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Aksi</th>
                    </tr>
                </thead>
                <tbody id="transactionsList">
                    <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td class="py-4 px-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">01 Jul 2024</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">14:30 WIB</div>
                        </td>
                        <td class="py-4 px-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">Bronze Plan - Juli 2024</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Langganan bulanan</div>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-4 bg-blue-600 rounded text-white text-xs flex items-center justify-center">V</div>
                                <span class="text-sm text-gray-700 dark:text-gray-300">•••• 4242</span>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">Rp 50.000</span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs px-2 py-1 rounded-full font-medium">Berhasil</span>
                        </td>
                        <td class="py-4 px-4">
                            <button onclick="viewInvoice('inv-001')" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
                                Invoice
                            </button>
                        </td>
                    </tr>
                    <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td class="py-4 px-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">01 Jun 2024</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">15:45 WIB</div>
                        </td>
                        <td class="py-4 px-4">
                            <div class="text-sm font-medium text-gray-900 dark:text-white">Bronze Plan - Juni 2024</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Langganan bulanan</div>
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-4 bg-green-600 rounded text-white text-xs flex items-center justify-center font-bold">D</div>
                                <span class="text-sm text-gray-700 dark:text-gray-300">Dana</span>
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">Rp 50.000</span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 text-xs px-2 py-1 rounded-full font-medium">Berhasil</span>
                        </td>
                        <td class="py-4 px-4">
                            <button onclick="viewInvoice('inv-002')" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
                                Invoice
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Load More -->
        <div class="mt-6 text-center">
            <button onclick="loadMoreTransactions()" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium">
                Muat Lebih Banyak
            </button>
        </div>
    </div>

    <!-- Billing Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Aksi Cepat</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Kelola langganan dan billing Anda</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <button onclick="upgradePlan()" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-lg font-medium transition-all duration-200 flex items-center gap-3">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                </svg>
                <div class="text-left">
                    <div class="font-semibold">Upgrade Plan</div>
                    <div class="text-xs opacity-90">Dapatkan fitur lebih</div>
                </div>
            </button>

            <button onclick="manageBilling()" class="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 p-4 rounded-lg font-medium transition-all duration-200 flex items-center gap-3">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <div class="text-left">
                    <div class="font-semibold">Kelola Billing</div>
                    <div class="text-xs opacity-70">Pengaturan pembayaran</div>
                </div>
            </button>

            <button onclick="cancelSubscription()" class="border border-red-300 dark:border-red-600 text-red-700 dark:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 p-4 rounded-lg font-medium transition-all duration-200 flex items-center gap-3">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <div class="text-left">
                    <div class="font-semibold">Batalkan Langganan</div>
                    <div class="text-xs opacity-70">Hentikan perpanjangan</div>
                </div>
            </button>
        </div>
    </div>
</div>

<!-- Live Chat Tab Content -->
<div id="tabContentLiveChat" class="hidden space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Live Chat Support</h2>
                <p class="text-gray-600 dark:text-gray-300">Hubungi tim customer service kami secara langsung</p>
            </div>
        </div>

        <!-- Support Status -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Status</p>
                        <p id="supportStatus" class="text-xs text-green-600 dark:text-green-400">Online</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">CS Available</p>
                        <p class="text-xs text-blue-600 dark:text-blue-400">3 Agents</p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Response Time</p>
                        <p class="text-xs text-purple-600 dark:text-purple-400">< 2 menit</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Interface -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-white font-semibold" id="csAgentName">Customer Service</h3>
                        <p class="text-blue-100 text-sm" id="csAgentStatus">Mengetik...</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-white text-sm">Online</span>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div id="chatMessages" class="h-96 overflow-y-auto p-6 space-y-4 bg-gray-50 dark:bg-gray-900">
            <!-- Welcome message -->
            <div class="flex items-start gap-3">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7 7z"></path>
                    </svg>
                </div>
                <div class="flex flex-col">
                    <div class="bg-white dark:bg-gray-800 rounded-2xl rounded-tl-sm px-4 py-3 shadow-sm border border-gray-200 dark:border-gray-700 max-w-xs">
                        <p class="text-sm text-gray-900 dark:text-white">Halo! Selamat datang di Sub4Short Support. Ada yang bisa kami bantu hari ini?</p>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-2">Baru saja</span>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800">
            <div class="flex items-end gap-3">
                <div class="flex-1">
                    <div class="relative">
                        <input
                            type="text"
                            id="chatInput"
                            class="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
                            placeholder="Ketik pesan Anda..."
                            maxlength="500"
                        >
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                            <button id="emojiBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                            <span id="charCount">0</span>/500 karakter
                        </span>
                        <div class="flex items-center gap-2">
                            <button id="attachBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 text-xs">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <button
                    id="sendBtn"
                    class="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-2xl transition-colors duration-200 flex items-center justify-center min-w-[48px] h-12 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center gap-3 mb-4">
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Pertanyaan Cepat</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Klik untuk mengirim pertanyaan umum</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <button onclick="sendQuickMessage('Bagaimana cara membuat shortlink?')" class="text-left p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div class="flex items-center gap-3">
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Cara membuat shortlink</span>
                </div>
            </button>

            <button onclick="sendQuickMessage('Bagaimana cara melihat analytics?')" class="text-left p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div class="flex items-center gap-3">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Melihat analytics</span>
                </div>
            </button>

            <button onclick="sendQuickMessage('Bagaimana cara upgrade plan?')" class="text-left p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div class="flex items-center gap-3">
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Upgrade plan</span>
                </div>
            </button>

            <button onclick="sendQuickMessage('Saya mengalami masalah teknis')" class="text-left p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div class="flex items-center gap-3">
                    <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Masalah teknis</span>
                </div>
            </button>
        </div>
    </div>
</div>



<style>
/* Upload Animation Styles */
@keyframes uploadSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.upload-success {
    animation: uploadSuccess 0.6s ease-in-out;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-shine::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progressShine 1.5s infinite;
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  transition: background 0.2s;
}
.toggle-track {
  width: 100%;
  height: 14px;
  background: #d1d5db;
  border-radius: 9999px;
  transition: background 0.2s;
  display: block;
}
.toggle-thumb {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 24px;
  height: 24px;
  background: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(59,130,246,0.15);
  transition: transform 0.2s, background 0.2s;
  will-change: transform;
}
.toggle-switch[aria-pressed="true"] .toggle-track {
  background: linear-gradient(90deg, #3b82f6, #6366f1);
}
.toggle-switch[aria-pressed="true"] .toggle-thumb {
  background: #2563eb;
  transform: translateX(20px);
}
.toggle-switch[aria-pressed="false"] .toggle-thumb {
  background: #d1d5db;
  transform: translateX(0);
}
.dark .toggle-switch[aria-pressed="true"] .toggle-track {
  background: linear-gradient(90deg, #60a5fa, #a78bfa);
}
.dark .toggle-switch[aria-pressed="true"] .toggle-thumb {
  background: #60a5fa;
}
.dark .toggle-switch[aria-pressed="false"] .toggle-thumb {
  background: #374151;
}
</style>

<script>
// Profile form AJAX
const profileForm = document.getElementById('profileForm');
const profileToast = document.getElementById('profileToast');
profileForm.addEventListener('submit', function(e) {
    e.preventDefault();
    const data = {
        name: document.getElementById('name').value,
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        bio: document.getElementById('bio').value
    };
    fetch('<?php echo e(route('profile.update')); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
        },
        body: JSON.stringify(data),
    })
    .then(res => res.json())
    .then(data => {
        showProfileToast(data.message || (data.success ? 'Profil berhasil diperbarui' : 'Gagal update profil'), data.success);
    })
    .catch(() => {
        showProfileToast('Gagal update profil', false);
    });
});

// Profile photo upload logic
const changePhotoBtn = document.getElementById('changePhotoBtn');
const profilePhotoInput = document.getElementById('profile-photo-input');
const profilePhotoPreview = document.getElementById('profilePhotoPreview');
const profilePhotoInitial = document.getElementById('profilePhotoInitial');
const photoUploadingSpinner = document.getElementById('photoUploadingSpinner');
const photoProgressBarContainer = document.getElementById('photoProgressBarContainer');
const photoProgressBar = document.getElementById('photoProgressBar');

changePhotoBtn.addEventListener('click', function() {
    profilePhotoInput.click();
});
profilePhotoInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        showProfileToast('Format file tidak didukung. Gunakan JPG, PNG, atau GIF.', false);
        return;
    }

    // Validate file size (2MB max)
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
        showProfileToast('Ukuran file terlalu besar. Maksimal 2MB.', false);
        return;
    }

    // Update button state
    const photoBtnIcon = document.getElementById('photoBtnIcon');
    const photoBtnText = document.getElementById('photoBtnText');
    changePhotoBtn.disabled = true;
    photoBtnIcon.className = 'fa-solid fa-spinner animate-spin';
    photoBtnText.textContent = 'Uploading...';

    // Show spinner and progress
    photoUploadingSpinner.style.display = 'flex';
    photoProgressBarContainer.style.display = 'block';
    photoProgressBar.style.width = '0%';
    // Prepare upload
    const formData = new FormData();
    formData.append('photo', file);
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '<?php echo e(route('profile.upload-photo')); ?>', true);
    xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name=csrf-token]').content);
    xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            photoProgressBar.style.width = percent + '%';
        }
    };
    xhr.onload = () => {
        // Reset button state
        const photoBtnIcon = document.getElementById('photoBtnIcon');
        const photoBtnText = document.getElementById('photoBtnText');
        changePhotoBtn.disabled = false;
        photoBtnIcon.className = 'fa-solid fa-camera';
        photoBtnText.textContent = 'Ganti Foto';

        photoUploadingSpinner.style.display = 'none';
        photoProgressBarContainer.style.display = 'none';
        if (xhr.status === 200) {
            try {
                const data = JSON.parse(xhr.responseText);
                if (data.success) {
                    // Update preview in settings with animation
                    profilePhotoPreview.src = data.photo_url;
                    profilePhotoPreview.style.display = 'block';
                    profilePhotoInitial.style.display = 'none';

                    // Add success animation
                    profilePhotoPreview.classList.add('upload-success');
                    setTimeout(() => {
                        profilePhotoPreview.classList.remove('upload-success');
                    }, 600);

                    // Update navbar profile photo without reload
                    updateNavbarProfilePhoto(data.photo_url);

                    // Play success sound
                    playSuccessSound();

                    showProfileToast('Foto profil berhasil diunggah!', true);
                } else {
                    showProfileToast('Upload gagal: ' + (data.message || 'Unknown error'), false);
                }
            } catch (e) {
                showProfileToast('Upload gagal: Response bukan JSON', false);
            }
        } else {
            showProfileToast('Upload gagal: Server error', false);
        }
    };
    xhr.onerror = () => {
        // Reset button state
        const photoBtnIcon = document.getElementById('photoBtnIcon');
        const photoBtnText = document.getElementById('photoBtnText');
        changePhotoBtn.disabled = false;
        photoBtnIcon.className = 'fa-solid fa-camera';
        photoBtnText.textContent = 'Ganti Foto';

        photoUploadingSpinner.style.display = 'none';
        photoProgressBarContainer.style.display = 'none';
        showProfileToast('Upload gagal: Network error', false);
    };
    xhr.send(formData);
});
function showProfileToast(msg, success) {
    profileToast.textContent = msg;
    profileToast.className = 'fixed z-50 right-4 bottom-4 md:right-8 md:bottom-8 px-4 py-3 rounded-lg shadow-lg text-white min-w-[220px] max-w-[90vw] ' + (success ? 'bg-green-600' : 'bg-red-600');
    profileToast.classList.remove('hidden');
    setTimeout(() => profileToast.classList.add('hidden'), 3000);
}

// Function to update navbar profile photo
function updateNavbarProfilePhoto(photoUrl) {
    // Update navbar profile images
    const navbarProfileImages = document.querySelectorAll('img[alt="Foto Profil"]');
    navbarProfileImages.forEach(img => {
        img.src = photoUrl;
        img.style.display = 'block';
    });

    // Hide profile initials in navbar
    const navbarProfileInitials = document.querySelectorAll('#profilePhotoInitial, [class*="bg-gradient-to-br"]');
    navbarProfileInitials.forEach(initial => {
        if (initial.tagName === 'DIV' && initial.querySelector('span')) {
            initial.style.display = 'none';
        }
    });

    // Dispatch custom event for Alpine.js components
    window.dispatchEvent(new CustomEvent('profile-photo-updated', {
        detail: { url: photoUrl }
    }));
}

// Function to play success sound
function playSuccessSound() {
    // Create audio context for success sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();

    // Success sound frequencies (happy chord)
    const frequencies = [523.25, 659.25, 783.99]; // C5, E5, G5
    const duration = 0.3;

    frequencies.forEach((freq, index) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);

        oscillator.start(audioContext.currentTime + index * 0.1);
        oscillator.stop(audioContext.currentTime + duration + index * 0.1);
    });
}

// Tab switching logic
const tabButtons = document.querySelectorAll('[data-tab]');
const tabContents = {
    profile: document.querySelector('.space-y-8'),
    security: document.getElementById('tabContentSecurity'),
    notifications: document.getElementById('tabContentNotifications'),
    billing: document.getElementById('tabContentBilling'),
    livechat: document.getElementById('tabContentLiveChat'),
};
function showTab(tab) {
    // Hide all tab contents
    Object.keys(tabContents).forEach(key => {
        const content = tabContents[key];
        if (content && content.classList) {
            content.classList.add('hidden');
        }
    });

    // Remove active classes from all buttons
    tabButtons.forEach(btn => {
        if (btn && btn.classList) {
            btn.classList.remove('border-blue-500', 'text-blue-600', 'dark:text-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20', 'active');
            // Reset SVG colors for Heroicons
            const svg = btn.querySelector('svg');
            if (svg) {
                svg.classList.remove('text-blue-600', 'dark:text-blue-400');
            }
        }
    });

    // Show selected tab content
    const selectedContent = tabContents[tab];
    if (selectedContent && selectedContent.classList) {
        selectedContent.classList.remove('hidden');
    }

    // Update active button
    const activeBtn = Array.from(tabButtons).find(b => b.dataset.tab === tab);
    if (activeBtn && activeBtn.classList) {
        activeBtn.classList.add('border-blue-500', 'text-blue-600', 'dark:text-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20', 'active');
        // Set SVG color for Heroicons
        const svg = activeBtn.querySelector('svg');
        if (svg) {
            svg.classList.add('text-blue-600', 'dark:text-blue-400');
        }
    }

    // Save to localStorage
    localStorage.setItem('settingsActiveTab', tab);
}
tabButtons.forEach(btn => {
    btn.addEventListener('click', function() {
        showTab(this.dataset.tab);
    });
});
// On page load, restore last tab
const lastTab = localStorage.getItem('settingsActiveTab') || 'profile';
showTab(lastTab);
// Live Chat Bubble logic
const startLiveChatBtn = document.getElementById('startLiveChatBtn');
const liveChatOverlay = document.getElementById('liveChatOverlay');
const liveChatBubble = document.getElementById('liveChatBubble');
const closeLiveChatBubble = document.getElementById('closeLiveChatBubble');
const liveChatLoading = document.getElementById('liveChatLoading');
const csMessageContainer = document.getElementById('csMessageContainer');
const liveChatLoadingDots = document.getElementById('liveChatLoadingDots');
const csConnectedContainer = document.getElementById('csConnectedContainer');
const csRandomName = document.getElementById('csRandomName');
// Animate dots ...
let dotsInterval = null;
function startDotsAnimation() {
    let dots = 0;
    if (liveChatLoadingDots) {
        liveChatLoadingDots.textContent = '';
        dotsInterval = setInterval(() => {
            dots = (dots + 1) % 4;
            liveChatLoadingDots.textContent = '.'.repeat(dots || 3); // always show at least 3 dots
        }, 400);
    }
}
function stopDotsAnimation() {
    if (dotsInterval) clearInterval(dotsInterval);
    if (liveChatLoadingDots) liveChatLoadingDots.textContent = '...';
}
function getRandomCSName() {
    const names = ['Dewi', 'Rizky', 'Andi', 'Sari', 'Budi', 'Putri', 'Agus', 'Lina', 'Fajar', 'Maya', 'Rina', 'Dian', 'Yusuf', 'Nina', 'Rama'];
    return names[Math.floor(Math.random() * names.length)];
}
if (startLiveChatBtn && liveChatOverlay && liveChatBubble && closeLiveChatBubble && liveChatLoading && csMessageContainer && csConnectedContainer && csRandomName) {
    startLiveChatBtn.addEventListener('click', function() {
        liveChatOverlay.classList.remove('hidden');
        liveChatLoading.style.display = 'flex';
        liveChatBubble.classList.add('hidden');
        csMessageContainer.classList.remove('opacity-100', 'translate-x-0');
        csMessageContainer.classList.add('opacity-0', '-translate-x-8');
        csConnectedContainer.classList.remove('opacity-100', 'translate-x-0');
        csConnectedContainer.classList.add('opacity-0', '-translate-x-8');
        startDotsAnimation();
        setTimeout(() => {
            liveChatLoading.style.display = 'none';
            liveChatBubble.classList.remove('hidden');
            stopDotsAnimation();
            // Set random CS name
            csRandomName.textContent = getRandomCSName();
            // Animate connected message in
            setTimeout(() => {
                csConnectedContainer.classList.remove('opacity-0', '-translate-x-8');
                csConnectedContainer.classList.add('opacity-100', 'translate-x-0');
                // After 1.2s, show CS message
                setTimeout(() => {
                    csMessageContainer.classList.remove('opacity-0', '-translate-x-8');
                    csMessageContainer.classList.add('opacity-100', 'translate-x-0');
                }, 1200);
            }, 200); // slight delay for smoothness
        }, 6000); // loading duration 6s
    });
    closeLiveChatBubble.addEventListener('click', function() {
        liveChatOverlay.classList.add('hidden');
        liveChatBubble.classList.add('hidden');
        liveChatLoading.style.display = 'flex';
        csMessageContainer.classList.remove('opacity-100', 'translate-x-0');
        csMessageContainer.classList.add('opacity-0', '-translate-x-8');
        csConnectedContainer.classList.remove('opacity-100', 'translate-x-0');
        csConnectedContainer.classList.add('opacity-0', '-translate-x-8');
        stopDotsAnimation();
    });
    liveChatOverlay.addEventListener('click', function(e) {
        if (e.target === liveChatOverlay) {
            liveChatOverlay.classList.add('hidden');
            liveChatBubble.classList.add('hidden');
            liveChatLoading.style.display = 'flex';
            csMessageContainer.classList.remove('opacity-100', 'translate-x-0');
            csMessageContainer.classList.add('opacity-0', '-translate-x-8');
            csConnectedContainer.classList.remove('opacity-100', 'translate-x-0');
            csConnectedContainer.classList.add('opacity-0', '-translate-x-8');
            stopDotsAnimation();
        }
    });
}
// Password visibility toggle
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('svg');

    if (input.type === 'password') {
        input.type = 'text';
        icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>';
    } else {
        input.type = 'password';
        icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
    }
}

// Password strength checker
function checkPasswordStrength(password) {
    let score = 0;
    let feedback = [];

    // Length check
    if (password.length >= 8) {
        score += 25;
        document.getElementById('req-length').innerHTML = '<svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span class="text-green-600 dark:text-green-400">Minimal 8 karakter</span>';
    } else {
        document.getElementById('req-length').innerHTML = '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg><span class="text-red-600 dark:text-red-400">Minimal 8 karakter</span>';
    }

    // Uppercase check
    if (/[A-Z]/.test(password)) {
        score += 25;
        document.getElementById('req-uppercase').innerHTML = '<svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span class="text-green-600 dark:text-green-400">Huruf besar (A-Z)</span>';
    } else {
        document.getElementById('req-uppercase').innerHTML = '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg><span class="text-red-600 dark:text-red-400">Huruf besar (A-Z)</span>';
    }

    // Lowercase check
    if (/[a-z]/.test(password)) {
        score += 25;
        document.getElementById('req-lowercase').innerHTML = '<svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span class="text-green-600 dark:text-green-400">Huruf kecil (a-z)</span>';
    } else {
        document.getElementById('req-lowercase').innerHTML = '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg><span class="text-red-600 dark:text-red-400">Huruf kecil (a-z)</span>';
    }

    // Number check
    if (/[0-9]/.test(password)) {
        score += 25;
        document.getElementById('req-number').innerHTML = '<svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span class="text-green-600 dark:text-green-400">Angka (0-9)</span>';
    } else {
        document.getElementById('req-number').innerHTML = '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg><span class="text-red-600 dark:text-red-400">Angka (0-9)</span>';
    }

    // Update strength bar
    const strengthBar = document.getElementById('passwordStrengthBar');
    const strengthText = document.getElementById('passwordStrengthText');

    strengthBar.style.width = score + '%';

    if (score < 50) {
        strengthBar.className = 'h-full transition-all duration-300 rounded-full bg-red-500';
        strengthText.textContent = 'Lemah';
        strengthText.className = 'text-xs font-medium text-red-600 dark:text-red-400';
    } else if (score < 75) {
        strengthBar.className = 'h-full transition-all duration-300 rounded-full bg-yellow-500';
        strengthText.textContent = 'Sedang';
        strengthText.className = 'text-xs font-medium text-yellow-600 dark:text-yellow-400';
    } else {
        strengthBar.className = 'h-full transition-all duration-300 rounded-full bg-green-500';
        strengthText.textContent = 'Kuat';
        strengthText.className = 'text-xs font-medium text-green-600 dark:text-green-400';
    }

    return score;
}

// Generate strong password
function generateStrongPassword() {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";

    // Ensure at least one character from each required type
    password += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(Math.random() * 26)]; // Uppercase
    password += "abcdefghijklmnopqrstuvwxyz"[Math.floor(Math.random() * 26)]; // Lowercase
    password += "0123456789"[Math.floor(Math.random() * 10)]; // Number
    password += "!@#$%^&*"[Math.floor(Math.random() * 8)]; // Special

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
        password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    password = password.split('').sort(() => Math.random() - 0.5).join('');

    document.getElementById('newPassword').value = password;
    document.getElementById('confirmPassword').value = password;

    checkPasswordStrength(password);
    checkPasswordMatch();

    // Show success message
    showSecurityToast('Password kuat berhasil di-generate!', 'success');
}

// Check password match
function checkPasswordMatch() {
    const newPass = document.getElementById('newPassword').value;
    const confirmPass = document.getElementById('confirmPassword').value;
    const indicator = document.getElementById('passwordMatchIndicator');

    if (confirmPass && newPass === confirmPass) {
        indicator.className = 'mt-2 text-xs text-green-600 dark:text-green-400';
        indicator.classList.remove('hidden');
    } else if (confirmPass) {
        indicator.className = 'mt-2 text-xs text-red-600 dark:text-red-400';
        indicator.innerHTML = '<span class="flex items-center gap-1"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>Password tidak cocok</span>';
        indicator.classList.remove('hidden');
    } else {
        indicator.classList.add('hidden');
    }
}
// Change Password Form Logic with real functionality
const changePasswordForm = document.getElementById('changePasswordForm');
const changePasswordBtn = document.getElementById('changePasswordBtn');
const changePasswordBtnText = document.getElementById('changePasswordBtnText');
const changePasswordSpinner = document.getElementById('changePasswordSpinner');
const changePasswordFeedback = document.getElementById('changePasswordFeedback');

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Password strength checker
    const newPasswordInput = document.getElementById('newPassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');

    if (newPasswordInput) {
        newPasswordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    }

    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', checkPasswordMatch);
    }
});

if (changePasswordForm) {
    changePasswordForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const oldPass = document.getElementById('oldPassword').value;
        const newPass = document.getElementById('newPassword').value;
        const confirmPass = document.getElementById('confirmPassword').value;

        // Validation
        if (!oldPass || !newPass || !confirmPass) {
            showSecurityFeedback('Semua field wajib diisi.', 'error');
            return;
        }

        if (newPass.length < 8) {
            showSecurityFeedback('Password baru minimal 8 karakter.', 'error');
            return;
        }

        if (newPass !== confirmPass) {
            showSecurityFeedback('Konfirmasi password tidak cocok.', 'error');
            return;
        }

        // Check password strength
        const strength = checkPasswordStrength(newPass);
        if (strength < 75) {
            showSecurityFeedback('Gunakan password yang lebih kuat untuk keamanan optimal.', 'warning');
            return;
        }

        // Start loading
        changePasswordBtn.disabled = true;
        changePasswordBtnText.textContent = 'Memproses...';
        changePasswordSpinner.classList.remove('hidden');
        changePasswordFeedback.classList.add('hidden');

        // Send AJAX request
        fetch('<?php echo e(route("profile.change-password")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
            },
            body: JSON.stringify({
                old_password: oldPass,
                new_password: newPass,
                new_password_confirmation: confirmPass
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSecurityFeedback('Password berhasil diganti!', 'success');
                changePasswordForm.reset();
                // Reset password requirements
                document.querySelectorAll('[id^="req-"]').forEach(req => {
                    req.innerHTML = req.innerHTML.replace('text-green-500', 'text-red-500').replace('text-green-600', 'text-red-600').replace('M5 13l4 4L19 7', 'M6 18L18 6M6 6l12 12');
                });
                document.getElementById('passwordStrengthBar').style.width = '0%';
                document.getElementById('passwordStrengthText').textContent = 'Lemah';
                document.getElementById('passwordMatchIndicator').classList.add('hidden');
            } else {
                showSecurityFeedback(data.message || 'Gagal mengganti password.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSecurityFeedback('Terjadi kesalahan. Silakan coba lagi.', 'error');
        })
        .finally(() => {
            changePasswordBtn.disabled = false;
            changePasswordBtnText.textContent = 'Ganti Password';
            changePasswordSpinner.classList.add('hidden');
        });
    });
}

// Security feedback function
function showSecurityFeedback(message, type) {
    const feedback = document.getElementById('changePasswordFeedback');
    feedback.textContent = message;

    if (type === 'success') {
        feedback.className = 'p-4 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300';
    } else if (type === 'warning') {
        feedback.className = 'p-4 rounded-lg bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-300';
    } else {
        feedback.className = 'p-4 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300';
    }

    feedback.classList.remove('hidden');
    setTimeout(() => {
        if (type === 'success') {
            feedback.classList.add('hidden');
        }
    }, 5000);
}

// Security toast function
function showSecurityToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white max-w-sm transform transition-all duration-300 translate-x-full`;

    if (type === 'success') {
        toast.classList.add('bg-green-600');
    } else if (type === 'warning') {
        toast.classList.add('bg-yellow-600');
    } else {
        toast.classList.add('bg-red-600');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Animate out
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 2FA Setup functions
function setup2FA(method) {
    if (method === 'app') {
        showSecurityToast('Fitur Authenticator App akan segera tersedia!', 'warning');
        // TODO: Implement authenticator app setup
    } else if (method === 'sms') {
        showSecurityToast('Fitur SMS Verification akan segera tersedia!', 'warning');
        // TODO: Implement SMS verification setup
    }
}

// Session management functions
function logoutSession(sessionId) {
    if (confirm('Yakin ingin logout dari sesi ini?')) {
        showSecurityToast('Sesi berhasil di-logout!', 'success');
        // TODO: Implement actual session logout
        document.querySelector(`[onclick="logoutSession('${sessionId}')"]`).closest('.flex').remove();
    }
}

function logoutAllSessions() {
    if (confirm('Yakin ingin logout dari semua sesi kecuali sesi ini?')) {
        showSecurityToast('Semua sesi lain berhasil di-logout!', 'success');
        // TODO: Implement actual logout all sessions
        const sessions = document.querySelectorAll('#activeSessions > div');
        sessions.forEach((session, index) => {
            if (index > 0) { // Keep current session (first one)
                session.remove();
            }
        });
    }
}

// Notification Settings Functions
let notificationSettings = {
    email_link_created: true,
    email_earnings: true,
    email_frequency: 'instant',
    push_link_clicks: false,
    push_earning_milestone: true,
    billing_reminder: true,
    billing_invoice: true
};

// Initialize notification settings on page load
document.addEventListener('DOMContentLoaded', function() {
    // Load saved settings from localStorage
    const saved = localStorage.getItem('notificationSettings');
    if (saved) {
        notificationSettings = { ...notificationSettings, ...JSON.parse(saved) };
    }

    // Apply saved settings to UI
    Object.keys(notificationSettings).forEach(key => {
        const element = document.getElementById(key.replace('_', ''));
        if (element && element.type === 'checkbox') {
            element.checked = notificationSettings[key];
        }
    });

    // Initialize push permission status
    checkPushPermission();
    updateNotificationStatusCards();
});

// Update notification setting
function updateNotificationSetting(setting, enabled) {
    notificationSettings[setting] = enabled;

    // Save to localStorage
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));

    // Send to backend
    fetch('<?php echo e(route("profile.update-notification-settings")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
        },
        body: JSON.stringify({ [setting]: enabled }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotificationToast(`Pengaturan ${setting.replace('_', ' ')} berhasil ${enabled ? 'diaktifkan' : 'dinonaktifkan'}!`, 'success');
        } else {
            showNotificationToast('Gagal menyimpan pengaturan', 'error');
            // Revert checkbox state
            const element = document.getElementById(setting.replace('_', ''));
            if (element) element.checked = !enabled;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotificationToast('Terjadi kesalahan', 'error');
        // Revert checkbox state
        const element = document.getElementById(setting.replace('_', ''));
        if (element) element.checked = !enabled;
    });

    updateNotificationStatusCards();
}

// Update email frequency
function updateEmailFrequency(frequency) {
    notificationSettings.email_frequency = frequency;
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));

    fetch('<?php echo e(route("profile.update-notification-settings")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
        },
        body: JSON.stringify({ email_frequency: frequency }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotificationToast(`Frekuensi email diubah ke ${frequency}`, 'success');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotificationToast('Gagal mengubah frekuensi email', 'error');
    });
}

// Update notification status cards
function updateNotificationStatusCards() {
    // Email status
    const emailActive = notificationSettings.email_link_created || notificationSettings.email_earnings;
    const emailStatus = document.getElementById('emailStatus');
    if (emailStatus) {
        emailStatus.textContent = emailActive ? 'Aktif' : 'Nonaktif';
        emailStatus.className = emailActive ? 'text-xs text-green-600 dark:text-green-400' : 'text-xs text-red-600 dark:text-red-400';
    }

    // Push status
    const pushActive = notificationSettings.push_link_clicks || notificationSettings.push_earning_milestone;
    const pushStatus = document.getElementById('pushStatus');
    if (pushStatus) {
        pushStatus.textContent = pushActive ? 'Aktif' : 'Nonaktif';
        pushStatus.className = pushActive ? 'text-xs text-green-600 dark:text-green-400' : 'text-xs text-red-600 dark:text-red-400';
    }

    // Billing status
    const billingActive = notificationSettings.billing_reminder || notificationSettings.billing_invoice;
    const billingStatus = document.getElementById('billingStatus');
    if (billingStatus) {
        billingStatus.textContent = billingActive ? 'Aktif' : 'Nonaktif';
        billingStatus.className = billingActive ? 'text-xs text-green-600 dark:text-green-400' : 'text-xs text-red-600 dark:text-red-400';
    }
}

// Push Notification Functions
function checkPushPermission() {
    const statusDiv = document.getElementById('pushPermissionStatus');
    const enableBtn = document.getElementById('enablePushBtn');

    if (!('Notification' in window)) {
        statusDiv.innerHTML = `
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <div>
                    <p class="font-medium text-red-800 dark:text-red-200">Browser Tidak Mendukung</p>
                    <p class="text-sm text-red-600 dark:text-red-300">Browser Anda tidak mendukung push notifications</p>
                </div>
            </div>
        `;
        statusDiv.className = 'mb-6 p-4 rounded-lg border bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
        enableBtn.style.display = 'none';
        return;
    }

    const permission = Notification.permission;

    if (permission === 'granted') {
        statusDiv.innerHTML = `
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <p class="font-medium text-green-800 dark:text-green-200">Push Notifications Aktif</p>
                    <p class="text-sm text-green-600 dark:text-green-300">Anda akan menerima notifikasi push</p>
                </div>
            </div>
        `;
        statusDiv.className = 'mb-6 p-4 rounded-lg border bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
        enableBtn.textContent = 'Push Notifications Aktif';
        enableBtn.disabled = true;
        enableBtn.className = 'flex-1 bg-gray-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium';
    } else if (permission === 'denied') {
        statusDiv.innerHTML = `
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <div>
                    <p class="font-medium text-red-800 dark:text-red-200">Push Notifications Diblokir</p>
                    <p class="text-sm text-red-600 dark:text-red-300">Aktifkan melalui pengaturan browser Anda</p>
                </div>
            </div>
        `;
        statusDiv.className = 'mb-6 p-4 rounded-lg border bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
        enableBtn.textContent = 'Diblokir - Cek Pengaturan Browser';
        enableBtn.disabled = true;
        enableBtn.className = 'flex-1 bg-red-400 text-white py-2 px-4 rounded-lg cursor-not-allowed font-medium';
    } else {
        statusDiv.innerHTML = `
            <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div>
                    <p class="font-medium text-yellow-800 dark:text-yellow-200">Push Notifications Belum Aktif</p>
                    <p class="text-sm text-yellow-600 dark:text-yellow-300">Klik tombol untuk mengaktifkan notifikasi push</p>
                </div>
            </div>
        `;
        statusDiv.className = 'mb-6 p-4 rounded-lg border bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
        enableBtn.textContent = 'Aktifkan Push Notifications';
        enableBtn.disabled = false;
        enableBtn.className = 'flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium';
    }
}

// Request push permission
function requestPushPermission() {
    if (!('Notification' in window)) {
        showNotificationToast('Browser tidak mendukung push notifications', 'error');
        return;
    }

    Notification.requestPermission().then(permission => {
        checkPushPermission();
        if (permission === 'granted') {
            showNotificationToast('Push notifications berhasil diaktifkan!', 'success');
            // Register service worker for push notifications
            registerServiceWorker();
        } else if (permission === 'denied') {
            showNotificationToast('Push notifications diblokir. Aktifkan melalui pengaturan browser.', 'error');
        }
    });
}

// Register service worker (simplified)
function registerServiceWorker() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker registered:', registration);
            })
            .catch(error => {
                console.log('Service Worker registration failed:', error);
            });
    }
}

// Test push notification
function testPushNotification() {
    if (Notification.permission !== 'granted') {
        showNotificationToast('Push notifications belum diaktifkan', 'warning');
        return;
    }

    const notification = new Notification('Test Notification', {
        body: 'Ini adalah test push notification dari Sub4Short!',
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'test-notification',
        requireInteraction: false,
        actions: [
            {
                action: 'view',
                title: 'Lihat Dashboard'
            }
        ]
    });

    notification.onclick = function() {
        window.focus();
        notification.close();
    };

    showNotificationToast('Test notification berhasil dikirim!', 'success');

    // Add to notification history
    addToNotificationHistory('Test Push Notification', 'Push', 'success');
}

// Notification toast function
function showNotificationToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white max-w-sm transform transition-all duration-300 translate-x-full`;

    if (type === 'success') {
        toast.classList.add('bg-green-600');
    } else if (type === 'warning') {
        toast.classList.add('bg-yellow-600');
    } else {
        toast.classList.add('bg-red-600');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Animate out
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Add to notification history
function addToNotificationHistory(title, type, status) {
    const historyContainer = document.getElementById('notificationHistory');
    if (!historyContainer) return;

    const now = new Date();
    const timeString = now.toLocaleString('id-ID', {
        day: 'numeric',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
    });

    const statusColors = {
        success: 'green',
        warning: 'yellow',
        error: 'red',
        info: 'blue'
    };

    const color = statusColors[status] || 'blue';

    const historyItem = document.createElement('div');
    historyItem.className = 'flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg';
    historyItem.innerHTML = `
        <div class="w-2 h-2 bg-${color}-500 rounded-full"></div>
        <div class="flex-1">
            <p class="text-sm font-medium text-gray-900 dark:text-white">${title}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">${type} • ${timeString}</p>
        </div>
        <span class="text-xs text-${color}-600 dark:text-${color}-400 bg-${color}-100 dark:bg-${color}-900/30 px-2 py-1 rounded-full">Terkirim</span>
    `;

    historyContainer.insertBefore(historyItem, historyContainer.firstChild);

    // Keep only last 10 items
    const items = historyContainer.children;
    if (items.length > 10) {
        historyContainer.removeChild(items[items.length - 1]);
    }
}

// Clear notification history
function clearNotificationHistory() {
    if (confirm('Yakin ingin menghapus semua riwayat notifikasi?')) {
        const historyContainer = document.getElementById('notificationHistory');
        if (historyContainer) {
            historyContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400">Riwayat notifikasi kosong</p>
                </div>
            `;
        }
        showNotificationToast('Riwayat notifikasi berhasil dihapus', 'success');
    }
}

// Load more history (placeholder)
function loadMoreHistory() {
    showNotificationToast('Fitur load more akan segera tersedia', 'info');
}

// Billing Functions
let billingData = {
    currentPlan: 'bronze',
    autoRenewal: true,
    paymentMethods: [
        { id: 'card-1', type: 'visa', last4: '4242', primary: true },
        { id: 'dana-1', type: 'dana', phone: '+62 812-3456-7890', primary: false }
    ],
    transactions: []
};

// Initialize billing data on page load
document.addEventListener('DOMContentLoaded', function() {
    // Load billing settings
    const savedBilling = localStorage.getItem('billingData');
    if (savedBilling) {
        billingData = { ...billingData, ...JSON.parse(savedBilling) };
    }

    // Apply auto-renewal setting
    const autoRenewalToggle = document.getElementById('autoRenewal');
    if (autoRenewalToggle) {
        autoRenewalToggle.checked = billingData.autoRenewal;
    }
});

// Upgrade plan function
function upgradePlan() {
    showBillingModal('upgrade', {
        title: 'Upgrade Plan',
        content: `
            <div class="space-y-4">
                <div class="text-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Pilih Plan Baru</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Upgrade untuk mendapatkan fitur lebih lengkap</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-colors" onclick="selectPlan('silver')">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white">Silver Plan</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Rp 100.000/bulan</p>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-600 dark:text-gray-300 space-y-1">
                            <li>• 500 Links</li>
                            <li>• Advanced Analytics</li>
                            <li>• Priority Support</li>
                            <li>• Custom Domain</li>
                        </ul>
                    </div>

                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-yellow-300 dark:hover:border-yellow-600 cursor-pointer transition-colors" onclick="selectPlan('gold')">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white">Gold Plan</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Rp 200.000/bulan</p>
                            </div>
                        </div>
                        <ul class="text-xs text-gray-600 dark:text-gray-300 space-y-1">
                            <li>• Unlimited Links</li>
                            <li>• Full Analytics Suite</li>
                            <li>• 24/7 Support</li>
                            <li>• API Access</li>
                            <li>• White Label</li>
                        </ul>
                    </div>
                </div>
            </div>
        `
    });
}

// Manage billing function
function manageBilling() {
    showBillingModal('manage', {
        title: 'Kelola Billing',
        content: `
            <div class="space-y-4">
                <div class="text-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Pengaturan Billing</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Kelola metode pembayaran dan pengaturan billing</p>
                </div>

                <div class="space-y-4">
                    <button onclick="addPaymentMethod()" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Tambah Metode Pembayaran
                    </button>
                    <button onclick="updateBillingAddress()" class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 py-3 px-4 rounded-lg transition-colors font-medium">
                        Update Alamat Billing
                    </button>
                    <button onclick="downloadInvoices()" class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 py-3 px-4 rounded-lg transition-colors font-medium">
                        Download Semua Invoice
                    </button>
                </div>
            </div>
        `
    });
}

// Cancel subscription function
function cancelSubscription() {
    showBillingModal('cancel', {
        title: 'Batalkan Langganan',
        content: `
            <div class="space-y-4">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-red-900 dark:text-red-200 mb-2">Yakin ingin membatalkan?</h3>
                    <p class="text-sm text-red-600 dark:text-red-300">Langganan akan berakhir pada 31 Desember 2024</p>
                </div>

                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <h4 class="font-medium text-red-900 dark:text-red-200 mb-2">Yang akan hilang:</h4>
                    <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                        <li>• Akses ke 100 links</li>
                        <li>• Email support</li>
                        <li>• Basic analytics</li>
                        <li>• Dashboard features</li>
                    </ul>
                </div>

                <div class="flex gap-3">
                    <button onclick="confirmCancelSubscription()" class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors font-medium">
                        Ya, Batalkan
                    </button>
                    <button onclick="closeBillingModal()" class="flex-1 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 py-2 px-4 rounded-lg transition-colors font-medium">
                        Batal
                    </button>
                </div>
            </div>
        `
    });
}

// Payment method functions
function addPaymentMethod() {
    showBillingModal('add-payment', {
        title: 'Tambah Metode Pembayaran',
        content: `
            <div class="space-y-4">
                <div class="text-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Pilih Metode Pembayaran</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Tambahkan metode pembayaran baru</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="addCreditCard()" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 transition-colors text-left">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-6 bg-blue-600 rounded flex items-center justify-center">
                                <svg class="w-6 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M1.5 8.67v8.58a3 3 0 003 3h15a3 3 0 003-3V8.67l-8.928 5.493a3 3 0 01-3.144 0L1.5 8.67z"/>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">Kartu Kredit/Debit</h4>
                                <p class="text-xs text-gray-600 dark:text-gray-300">Visa, Mastercard, dll</p>
                            </div>
                        </div>
                    </button>

                    <button onclick="addEWallet()" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-green-300 dark:hover:border-green-600 transition-colors text-left">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-6 bg-green-600 rounded flex items-center justify-center">
                                <span class="text-white font-bold text-xs">E</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">E-Wallet</h4>
                                <p class="text-xs text-gray-600 dark:text-gray-300">Dana, OVO, GoPay</p>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        `
    });
}

function editPaymentMethod(methodId) {
    showBillingToast('Fitur edit payment method akan segera tersedia', 'info');
}

function deletePaymentMethod(methodId) {
    if (confirm('Yakin ingin menghapus metode pembayaran ini?')) {
        showBillingToast('Metode pembayaran berhasil dihapus', 'success');
        // TODO: Implement actual deletion
    }
}

function setPrimaryPayment(methodId) {
    showBillingToast('Metode pembayaran utama berhasil diubah', 'success');
    // TODO: Implement actual primary setting
}

// Auto-renewal toggle
function toggleAutoRenewal(enabled) {
    billingData.autoRenewal = enabled;
    localStorage.setItem('billingData', JSON.stringify(billingData));

    // Send to backend
    fetch('<?php echo e(route("profile.update-billing-settings")); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
        },
        body: JSON.stringify({ auto_renewal: enabled }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showBillingToast(`Perpanjangan otomatis ${enabled ? 'diaktifkan' : 'dinonaktifkan'}`, 'success');
        } else {
            showBillingToast('Gagal mengubah pengaturan', 'error');
            // Revert toggle
            document.getElementById('autoRenewal').checked = !enabled;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showBillingToast('Terjadi kesalahan', 'error');
        // Revert toggle
        document.getElementById('autoRenewal').checked = !enabled;
    });
}

// Transaction functions
function viewInvoice(invoiceId) {
    showBillingToast('Membuka invoice...', 'info');
    // TODO: Open invoice in new tab
    setTimeout(() => {
        window.open(`/invoice/${invoiceId}`, '_blank');
    }, 1000);
}

function exportTransactions() {
    showBillingToast('Mengekspor transaksi...', 'info');
    // TODO: Generate and download CSV/PDF
    setTimeout(() => {
        showBillingToast('File transaksi berhasil diunduh', 'success');
    }, 2000);
}

function filterTransactions() {
    showBillingModal('filter', {
        title: 'Filter Transaksi',
        content: `
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Periode</label>
                    <select class="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option>Semua waktu</option>
                        <option>30 hari terakhir</option>
                        <option>3 bulan terakhir</option>
                        <option>1 tahun terakhir</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                    <select class="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option>Semua status</option>
                        <option>Berhasil</option>
                        <option>Pending</option>
                        <option>Gagal</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Metode Pembayaran</label>
                    <select class="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option>Semua metode</option>
                        <option>Kartu Kredit</option>
                        <option>Dana</option>
                        <option>OVO</option>
                    </select>
                </div>
                <div class="flex gap-3">
                    <button onclick="applyTransactionFilter()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Terapkan Filter
                    </button>
                    <button onclick="resetTransactionFilter()" class="flex-1 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 py-2 px-4 rounded-lg transition-colors font-medium">
                        Reset
                    </button>
                </div>
            </div>
        `
    });
}

function loadMoreTransactions() {
    showBillingToast('Memuat transaksi...', 'info');
    // TODO: Load more transactions from backend
    setTimeout(() => {
        showBillingToast('Transaksi berhasil dimuat', 'success');
    }, 1500);
}

// Utility functions
function showBillingModal(type, options) {
    // Close existing modal if any
    if (window.currentBillingModal) {
        closeBillingModal();
    }

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto transform scale-95 transition-transform duration-200" onclick="event.stopPropagation()">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">${options.title}</h2>
                    <button id="closeBillingModalBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="billing-modal-content">
                    ${options.content}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Store reference for closing
    window.currentBillingModal = modal;

    // Animate in
    setTimeout(() => {
        modal.style.opacity = '1';
        const modalContent = modal.querySelector('.bg-white, .bg-gray-800');
        if (modalContent) {
            modalContent.style.transform = 'scale(1)';
        }
    }, 10);

    // Close on backdrop click
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeBillingModal();
        }
    });

    // Close on X button click
    const closeBtn = modal.querySelector('#closeBillingModalBtn');
    if (closeBtn) {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeBillingModal();
        });
    }

    // Close on Escape key
    const handleEscape = function(e) {
        if (e.key === 'Escape') {
            closeBillingModal();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);

    // Store escape handler for cleanup
    modal.escapeHandler = handleEscape;
}

function closeBillingModal() {
    if (window.currentBillingModal) {
        // Remove escape key listener
        if (window.currentBillingModal.escapeHandler) {
            document.removeEventListener('keydown', window.currentBillingModal.escapeHandler);
        }

        // Add fade out animation
        window.currentBillingModal.style.opacity = '0';
        const modalContent = window.currentBillingModal.querySelector('.bg-white, .bg-gray-800');
        if (modalContent) {
            modalContent.style.transform = 'scale(0.95)';
        }

        setTimeout(() => {
            if (window.currentBillingModal && document.body.contains(window.currentBillingModal)) {
                document.body.removeChild(window.currentBillingModal);
            }
            window.currentBillingModal = null;
        }, 200);
    }
}

function showBillingToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white max-w-sm transform transition-all duration-300 translate-x-full`;

    if (type === 'success') {
        toast.classList.add('bg-green-600');
    } else if (type === 'warning') {
        toast.classList.add('bg-yellow-600');
    } else if (type === 'info') {
        toast.classList.add('bg-blue-600');
    } else {
        toast.classList.add('bg-red-600');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Animate out
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Plan selection and confirmation functions
function selectPlan(planType) {
    const plans = {
        silver: { name: 'Silver Plan', price: 'Rp 100.000', features: ['500 Links', 'Advanced Analytics', 'Priority Support'] },
        gold: { name: 'Gold Plan', price: 'Rp 200.000', features: ['Unlimited Links', 'Full Analytics', '24/7 Support', 'API Access'] }
    };

    const plan = plans[planType];
    if (!plan) return;

    closeBillingModal();

    showBillingModal('confirm-upgrade', {
        title: 'Konfirmasi Upgrade',
        content: `
            <div class="space-y-4">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Upgrade ke ${plan.name}</h3>
                    <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">${plan.price}/bulan</p>
                </div>

                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 dark:text-blue-200 mb-2">Fitur yang akan Anda dapatkan:</h4>
                    <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                        ${plan.features.map(feature => `<li>• ${feature}</li>`).join('')}
                    </ul>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-300">Bronze Plan (sisa 23 hari)</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">-Rp 38.333</span>
                    </div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-gray-600 dark:text-gray-300">${plan.name}</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">${plan.price}</span>
                    </div>
                    <hr class="my-2 border-gray-200 dark:border-gray-600">
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-900 dark:text-white">Total hari ini</span>
                        <span class="font-bold text-gray-900 dark:text-white">Rp ${planType === 'silver' ? '61.667' : '161.667'}</span>
                    </div>
                </div>

                <div class="flex gap-3">
                    <button onclick="confirmUpgrade('${planType}')" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Konfirmasi Upgrade
                    </button>
                    <button onclick="closeBillingModal()" class="flex-1 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 py-2 px-4 rounded-lg transition-colors font-medium">
                        Batal
                    </button>
                </div>
            </div>
        `
    });
}

function confirmUpgrade(planType) {
    closeBillingModal();
    showBillingToast('Memproses upgrade...', 'info');

    // Simulate upgrade process
    setTimeout(() => {
        showBillingToast(`Berhasil upgrade ke ${planType.charAt(0).toUpperCase() + planType.slice(1)} Plan!`, 'success');
        // TODO: Refresh page or update UI
    }, 2000);
}

function confirmCancelSubscription() {
    closeBillingModal();
    showBillingToast('Memproses pembatalan...', 'info');

    // Simulate cancellation process
    setTimeout(() => {
        showBillingToast('Langganan berhasil dibatalkan', 'success');
        // TODO: Update UI to reflect cancellation
    }, 2000);
}

// Additional utility functions
function addCreditCard() {
    closeBillingModal();
    showBillingToast('Fitur tambah kartu kredit akan segera tersedia', 'info');
}

function addEWallet() {
    closeBillingModal();
    showBillingToast('Fitur tambah e-wallet akan segera tersedia', 'info');
}

function updateBillingAddress() {
    closeBillingModal();
    showBillingToast('Fitur update alamat billing akan segera tersedia', 'info');
}

function downloadInvoices() {
    closeBillingModal();
    showBillingToast('Mengunduh semua invoice...', 'info');
    setTimeout(() => {
        showBillingToast('Invoice berhasil diunduh', 'success');
    }, 2000);
}

function applyTransactionFilter() {
    closeBillingModal();
    showBillingToast('Filter diterapkan', 'success');
    // TODO: Apply actual filter
}

function resetTransactionFilter() {
    closeBillingModal();
    showBillingToast('Filter direset', 'info');
    // TODO: Reset actual filter
}

// Global function to ensure modal can be closed from anywhere
window.closeBillingModal = closeBillingModal;

// Add global click handler for modal close buttons
document.addEventListener('DOMContentLoaded', function() {
    // Handle any dynamically created close buttons
    document.addEventListener('click', function(e) {
        if (e.target.matches('[onclick*="closeBillingModal"]') ||
            e.target.closest('[onclick*="closeBillingModal"]')) {
            e.preventDefault();
            closeBillingModal();
        }
    });
});

// Live Chat Functions
let chatMessages = [];
let isTyping = false;
let csAgent = {
    name: 'Customer Service',
    status: 'Online'
};

// Initialize chat on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
});

function initializeChat() {
    const chatInput = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendBtn');
    const charCount = document.getElementById('charCount');
    const emojiBtn = document.getElementById('emojiBtn');
    const attachBtn = document.getElementById('attachBtn');

    if (!chatInput || !sendBtn) return;

    // Set random CS agent name
    const csNames = ['Dewi', 'Rizky', 'Andi', 'Sari', 'Budi', 'Putri', 'Agus', 'Lina', 'Fajar', 'Maya'];
    csAgent.name = csNames[Math.floor(Math.random() * csNames.length)];
    document.getElementById('csAgentName').textContent = csAgent.name;

    // Input event listeners
    chatInput.addEventListener('input', function() {
        const length = this.value.length;
        charCount.textContent = length;
        sendBtn.disabled = length === 0;

        if (length === 0) {
            sendBtn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
        } else {
            sendBtn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
        }
    });

    // Send message on Enter
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Send button click
    sendBtn.addEventListener('click', sendMessage);

    // Emoji button (placeholder)
    if (emojiBtn) {
        emojiBtn.addEventListener('click', function() {
            showChatToast('Fitur emoji akan segera tersedia!', 'info');
        });
    }

    // Attach button (placeholder)
    if (attachBtn) {
        attachBtn.addEventListener('click', function() {
            showChatToast('Fitur attachment akan segera tersedia!', 'info');
        });
    }

    // Show initial typing indicator
    setTimeout(() => {
        showTypingIndicator();
        setTimeout(() => {
            hideTypingIndicator();
        }, 2000);
    }, 1000);
}

function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();

    if (!message) return;

    // Add user message
    addMessage(message, 'user');
    chatInput.value = '';
    document.getElementById('charCount').textContent = '0';
    document.getElementById('sendBtn').disabled = true;

    // Show typing indicator
    showTypingIndicator();

    // Simulate CS response
    setTimeout(() => {
        hideTypingIndicator();
        const response = generateCSResponse(message);
        addMessage(response, 'cs');
    }, Math.random() * 2000 + 1000); // 1-3 seconds delay
}

function addMessage(text, sender) {
    const chatMessagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    const timestamp = new Date().toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
    });

    if (sender === 'user') {
        messageDiv.className = 'flex items-start gap-3 justify-end';
        messageDiv.innerHTML = `
            <div class="flex flex-col items-end">
                <div class="bg-blue-600 text-white rounded-2xl rounded-tr-sm px-4 py-3 shadow-sm max-w-xs">
                    <p class="text-sm">${text}</p>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 mr-2">${timestamp}</span>
            </div>
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
        `;
    } else {
        messageDiv.className = 'flex items-start gap-3';
        messageDiv.innerHTML = `
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div class="flex flex-col">
                <div class="bg-white dark:bg-gray-800 rounded-2xl rounded-tl-sm px-4 py-3 shadow-sm border border-gray-200 dark:border-gray-700 max-w-xs">
                    <p class="text-sm text-gray-900 dark:text-white">${text}</p>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-2">${timestamp}</span>
            </div>
        `;
    }

    chatMessagesContainer.appendChild(messageDiv);
    chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;

    // Add to messages array
    chatMessages.push({ text, sender, timestamp });
}

function showTypingIndicator() {
    if (isTyping) return;

    isTyping = true;
    const statusElement = document.getElementById('csAgentStatus');
    if (statusElement) {
        statusElement.textContent = 'Mengetik...';
    }

    const chatMessagesContainer = document.getElementById('chatMessages');
    const typingDiv = document.createElement('div');
    typingDiv.id = 'typingIndicator';
    typingDiv.className = 'flex items-start gap-3';
    typingDiv.innerHTML = `
        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
            <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7-7h14a7 7 0 00-7-7z"></path>
            </svg>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-2xl rounded-tl-sm px-4 py-3 shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="flex items-center gap-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
        </div>
    `;

    chatMessagesContainer.appendChild(typingDiv);
    chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
}

function hideTypingIndicator() {
    isTyping = false;
    const statusElement = document.getElementById('csAgentStatus');
    if (statusElement) {
        statusElement.textContent = 'Online';
    }

    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function generateCSResponse(userMessage) {
    const message = userMessage.toLowerCase();

    // Predefined responses based on keywords
    if (message.includes('shortlink') || message.includes('link')) {
        return 'Untuk membuat shortlink, Anda bisa masuk ke dashboard dan klik tombol "Buat Link Baru". Masukkan URL yang ingin dipendekkan, lalu klik "Generate". Apakah ada yang ingin ditanyakan lagi?';
    }

    if (message.includes('analytics') || message.includes('statistik')) {
        return 'Analytics dapat dilihat di dashboard Anda. Klik pada link yang sudah dibuat untuk melihat detail klik, lokasi pengunjung, dan statistik lainnya. Fitur analytics lengkap tersedia di plan Silver dan Gold.';
    }

    if (message.includes('upgrade') || message.includes('plan')) {
        return 'Untuk upgrade plan, Anda bisa ke menu Settings > Billing, lalu pilih "Upgrade Plan". Tersedia Silver Plan (Rp 100.000/bulan) dan Gold Plan (Rp 200.000/bulan) dengan fitur lebih lengkap.';
    }

    if (message.includes('masalah') || message.includes('error') || message.includes('tidak bisa')) {
        return 'Maaf mendengar Anda mengalami masalah. Bisakah Anda jelaskan lebih detail masalah yang dialami? Saya akan bantu troubleshoot step by step.';
    }

    if (message.includes('pembayaran') || message.includes('bayar')) {
        return 'Untuk pembayaran, kami menerima berbagai metode seperti kartu kredit, Dana, OVO, dan GoPay. Pembayaran diproses secara otomatis dan langganan akan aktif dalam 1-2 menit.';
    }

    if (message.includes('halo') || message.includes('hai') || message.includes('hello')) {
        return 'Halo! Selamat datang di Sub4Short Support. Saya siap membantu Anda hari ini. Ada yang bisa saya bantu?';
    }

    if (message.includes('terima kasih') || message.includes('thanks')) {
        return 'Sama-sama! Senang bisa membantu Anda. Jika ada pertanyaan lain, jangan ragu untuk bertanya ya! 😊';
    }

    // Default responses
    const defaultResponses = [
        'Terima kasih atas pertanyaannya. Bisa tolong dijelaskan lebih detail agar saya bisa membantu dengan lebih baik?',
        'Saya akan bantu Anda dengan senang hati. Bisakah Anda berikan informasi lebih lengkap tentang hal tersebut?',
        'Pertanyaan yang menarik! Untuk memberikan jawaban yang tepat, bisa dijelaskan konteksnya lebih detail?',
        'Saya siap membantu! Agar bisa memberikan solusi terbaik, mohon berikan detail lebih lengkap ya.',
    ];

    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
}

function sendQuickMessage(message) {
    document.getElementById('chatInput').value = message;
    sendMessage();
}

function showChatToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white max-w-sm transform transition-all duration-300 translate-x-full`;

    if (type === 'success') {
        toast.classList.add('bg-green-600');
    } else if (type === 'warning') {
        toast.classList.add('bg-yellow-600');
    } else if (type === 'info') {
        toast.classList.add('bg-blue-600');
    } else {
        toast.classList.add('bg-red-600');
    }

    toast.textContent = message;
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Animate out
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

</script>

</div> <!-- End of Settings Container --><?php /**PATH C:\laragon\www\sub4short--plus\resources\views/pages/settings.blade.php ENDPATH**/ ?>