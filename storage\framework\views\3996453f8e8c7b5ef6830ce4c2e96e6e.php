<!-- Header -->
<div class="flex items-center justify-between mb-8">
    <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Pengaturan</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1"><PERSON><PERSON><PERSON> profil dan preferensi akun <PERSON></p>
    </div>
</div>

<!-- Settings Navigation -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-0 mb-8 transition-all duration-300">
    <div class="flex overflow-x-auto no-scrollbar border-b border-gray-200 dark:border-gray-700 px-2 md:px-6 gap-1 md:gap-2">
        <button id="tabProfile" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="profile">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span class="text-sm">Profil</span>
        </button>
        <button id="tabSecurity" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="security">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <span class="text-sm">Keamanan</span>
        </button>
        <button id="tabNotifications" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="notifications">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5V13"></path>
            </svg>
            <span class="text-sm">Notifikasi</span>
        </button>
        <button id="tabBilling" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="billing">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span class="text-sm">Billing</span>
        </button>
        <button id="tabLiveChat" class="flex flex-col items-center justify-center px-4 py-4 font-medium transition-all duration-200 min-w-[100px] md:min-w-[120px] focus:outline-none border-b-2 group text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400" data-tab="livechat">
            <svg class="w-5 h-5 mb-2 transition-all duration-200 group-hover:text-blue-500 dark:group-hover:text-blue-400 group-active:text-blue-600 dark:group-active:text-blue-400 group-[.active]:text-blue-600 dark:group-[.active]:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <span class="text-sm">Live Chat</span>
        </button>
    </div>
</div>

<!-- Profile Settings -->
<div class="space-y-8">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-100 dark:border-gray-700 p-4 md:p-6 transition-all duration-300 mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            Informasi Profil
        </h2>
        <form id="profileForm" class="space-y-6">
            <div class="flex items-center space-x-4 md:space-x-6">
                <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center overflow-hidden relative">
                    <img id="profilePhotoPreview" src="<?php echo e(optional(Auth::user())->profile_photo ? asset(optional(Auth::user())->profile_photo) : ''); ?>" alt="Foto Profil" class="w-full h-full object-cover rounded-full" style="display: <?php echo e(optional(Auth::user())->profile_photo ? 'block' : 'none'); ?>;">
                    <span id="profilePhotoInitial" class="text-white text-xl md:text-2xl font-bold" style="display: <?php echo e(optional(Auth::user())->profile_photo ? 'none' : 'block'); ?>;">U</span>
                    <div id="photoUploadingSpinner" class="absolute inset-0 flex items-center justify-center rounded-full bg-black/40" style="display:none;">
                        <div class="w-10 h-10 border-4 border-blue-400 border-t-transparent border-solid rounded-full animate-spin"></div>
                    </div>
                    <div id="photoProgressBarContainer" class="absolute bottom-0 left-0 right-0 h-2 bg-gray-200 dark:bg-gray-700 rounded-b-full overflow-hidden" style="display:none;">
                        <div id="photoProgressBar" class="h-2 bg-blue-500 transition-all duration-200" style="width:0%"></div>
                    </div>
                </div>
                <div>
                    <input type="file" id="profile-photo-input" class="hidden" accept="image/*">
                    <button type="button" id="changePhotoBtn" class="bg-blue-600 dark:bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200">
                        <i class="fa-solid fa-camera mr-2"></i>
                        <span id="photoBtnText">Ganti Foto</span>
                    </button>
                    <input type="file" id="fileInput" class="hidden" accept="image/*">
                    <p class="text-xs md:text-sm text-gray-500 dark:text-gray-400 mt-1">JPG, PNG atau GIF. Maksimal 2MB.</p>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div>
                    <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nama Lengkap</label>
                    <input type="text" id="name" value="<?php echo e(optional(Auth::user())->name ?: 'Nama tidak ditemukan'); ?>" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 <?php echo e(optional(Auth::user())->name ? '' : 'italic text-red-500 dark:text-red-400'); ?>">
                </div>
                <div>
                    <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Username</label>
                    <input type="text" id="username" value="<?php echo e(optional(Auth::user())->username); ?>" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400">
                </div>
            </div>
            <div>
                <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                <input type="email" id="email" value="<?php echo e(optional(Auth::user())->email ?: 'Email tidak ditemukan'); ?>" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 <?php echo e(optional(Auth::user())->email ? '' : 'italic text-red-500 dark:text-red-400'); ?>">
            </div>
            <div>
                <label class="block text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bio</label>
                <textarea rows="3" id="bio" placeholder="Ceritakan sedikit tentang diri Anda..." class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 resize-none"><?php echo e(optional(Auth::user())->bio); ?></textarea>
            </div>
            <div class="flex justify-end sticky bottom-0 bg-white dark:bg-gray-800 py-2 z-10 md:static md:bg-transparent">
                <button type="submit" class="w-full md:w-auto bg-blue-600 dark:bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 font-semibold">
                    Simpan Perubahan
                </button>
            </div>
        </form>
        <!-- Toast Notification -->
        <div id="profileToast" class="fixed z-50 right-4 bottom-4 md:right-8 md:bottom-8 px-4 py-3 rounded-lg shadow-lg text-white hidden min-w-[220px] max-w-[90vw]"></div>
    </div>
</div>

<!-- Security Settings -->
<div id="tabContentSecurity" class="hidden space-y-8">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-100 dark:border-gray-700 p-6 transition-all duration-300 mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-1 flex items-center gap-3">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            Keamanan Akun
        </h2>
        <p class="text-gray-500 dark:text-gray-400 mb-6">Kelola keamanan akun Anda, ganti password, aktifkan 2FA, dan pantau sesi login.</p>
        <!-- Change Password Form -->
        <form id="changePasswordForm" class="space-y-5 max-w-lg">
            <div>
                <label class="block text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2">Password Lama</label>
                <input type="password" id="oldPassword" class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 text-sm" placeholder="Masukkan password lama" required>
            </div>
            <div>
                <label class="block text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2">Password Baru</label>
                <input type="password" id="newPassword" class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 text-sm" placeholder="Password baru" required>
            </div>
            <div>
                <label class="block text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2">Konfirmasi Password Baru</label>
                <input type="password" id="confirmPassword" class="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 text-sm" placeholder="Ulangi password baru" required>
            </div>
            <div class="flex justify-end">
                <button type="submit" id="changePasswordBtn" class="w-full md:w-auto bg-blue-600 dark:bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 font-semibold flex items-center gap-2">
                    <span id="changePasswordBtnText">Ganti Password</span>
                    <svg id="changePasswordSpinner" class="hidden animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
                </button>
            </div>
            <div id="changePasswordFeedback" class="mt-3 text-sm font-semibold hidden"></div>
        </form>
        <!-- 2FA Section -->
        <div class="mt-8 flex items-center gap-4">
            <i class="fa-solid fa-mobile-screen-button text-blue-500 text-2xl"></i>
            <div class="flex-1">
                <div class="font-semibold text-gray-900 dark:text-white">Two-Factor Authentication (2FA)</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">Perlindungan ekstra dengan kode OTP saat login.</div>
            </div>
            <button id="toggle2FA" class="px-4 py-2 rounded-lg font-semibold transition-all duration-200 text-white" :class="twoFAEnabled ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400 hover:bg-gray-500'">
                <span x-text="twoFAEnabled ? 'Aktif' : 'Nonaktif'"></span>
            </button>
        </div>
        <!-- Active Sessions (dummy) -->
        <div class="mt-8">
            <div class="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2"><i class="fa-solid fa-desktop text-blue-500"></i> Sesi Login Aktif</div>
            <div class="space-y-2">
                <div class="flex items-center justify-between bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-2">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-laptop text-blue-400"></i>
                        <span class="text-sm">Chrome - Windows 10</span>
                        <span class="text-xs bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded px-2 py-0.5 ml-2">Aktif</span>
                    </div>
                    <button class="text-xs text-red-600 hover:underline">Logout</button>
                </div>
                <div class="flex items-center justify-between bg-gray-100 dark:bg-gray-700 rounded-lg px-4 py-2">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-mobile-alt text-blue-400"></i>
                        <span class="text-sm">Safari - iPhone</span>
                        <span class="text-xs bg-gray-200 text-gray-700 dark:bg-gray-800 dark:text-gray-300 rounded px-2 py-0.5 ml-2">Tidak aktif</span>
                    </div>
                    <button class="text-xs text-red-600 hover:underline">Logout</button>
                </div>
            </div>
        </div>
        <!-- Security Tips -->
        <div class="mt-8">
            <div class="font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2"><i class="fa-solid fa-lightbulb text-yellow-400"></i> Tips Keamanan</div>
            <ul class="list-disc pl-6 text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>Gunakan password unik dan kuat.</li>
                <li>Jangan bagikan password ke siapa pun.</li>
                <li>Aktifkan 2FA untuk perlindungan ekstra.</li>
                <li>Logout dari perangkat yang tidak dikenal.</li>
            </ul>
        </div>
    </div>
</div>

<!-- Notifications Settings -->
<div id="tabContentNotifications" class="hidden space-y-8">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-100 dark:border-gray-700 p-6 transition-all duration-300 mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5V13"></path>
                </svg>
            </div>
            Pengaturan Notifikasi
        </h2>
        <div class="flex flex-col gap-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Email notifikasi aktif</span>
                </div>
                <button type="button" class="toggle-switch" id="notifEmailToggle" aria-pressed="true">
                    <span class="toggle-track"></span>
                    <span class="toggle-thumb"></span>
                </button>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5V13"></path>
                        </svg>
                    </div>
                    <span class="font-medium">Push notifikasi browser</span>
                </div>
                <button type="button" class="toggle-switch" id="notifPushToggle" aria-pressed="false">
                    <span class="toggle-track"></span>
                    <span class="toggle-thumb"></span>
                </button>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <i class="fa-solid fa-receipt text-blue-400"></i>
                    <span class="font-medium">Notifikasi transaksi &amp; billing</span>
                </div>
                <button type="button" class="toggle-switch" id="notifBillingToggle" aria-pressed="true">
                    <span class="toggle-track"></span>
                    <span class="toggle-thumb"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Billing Settings -->
<div id="tabContentBilling" class="hidden space-y-8">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-100 dark:border-gray-700 p-6 transition-all duration-300 mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2 flex items-center gap-3">
            <div class="w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>
            Informasi Billing
        </h2>
        <p class="text-gray-600 dark:text-gray-300 mb-6">Kelola langganan, pembayaran, dan riwayat transaksi Anda dengan mudah dan aman.</p>
        <!-- Plan Info -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-xl flex items-center justify-center">
                    <i class="fa-solid fa-crown text-white text-2xl"></i>
                </div>
                <div>
                    <div class="flex items-center gap-2">
                        <span class="text-lg font-bold text-gray-900 dark:text-white">Bronze Plan</span>
                        <span class="text-xs bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 rounded px-2 py-0.5 font-semibold">Aktif</span>
                    </div>
                    <div class="text-xs text-gray-600 dark:text-gray-300">Fitur: Limit 100 link, Support Email, Dashboard Analytics</div>
                </div>
            </div>
            <div class="flex flex-col items-end">
                <span class="text-xs text-gray-500 dark:text-gray-400">Masa aktif hingga</span>
                <span class="font-semibold text-blue-600 dark:text-blue-400">31 Desember 2024</span>
            </div>
        </div>
        <!-- Next Payment -->
        <div class="flex items-center gap-3 mb-6">
            <i class="fa-solid fa-calendar text-blue-500"></i>
            <span class="font-semibold text-gray-900 dark:text-white">Pembayaran Berikutnya:</span>
            <span class="text-sm text-gray-700 dark:text-gray-200">Tidak ada pembayaran terjadwal</span>
            <span class="ml-2 text-xs bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded px-2 py-0.5">Lunas</span>
        </div>
        <!-- Transaction History -->
        <div class="mb-6">
            <div class="flex items-center gap-2 mb-2">
                <i class="fa-solid fa-receipt text-blue-500"></i>
                <span class="font-semibold text-gray-900 dark:text-white">Riwayat Transaksi</span>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm text-left border rounded-xl overflow-hidden">
                    <thead class="bg-gray-100 dark:bg-gray-700">
                        <tr>
                            <th class="px-4 py-2 font-semibold text-gray-700 dark:text-gray-200">Tanggal</th>
                            <th class="px-4 py-2 font-semibold text-gray-700 dark:text-gray-200">Deskripsi</th>
                            <th class="px-4 py-2 font-semibold text-gray-700 dark:text-gray-200">Jumlah</th>
                            <th class="px-4 py-2 font-semibold text-gray-700 dark:text-gray-200">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="px-4 py-2 text-gray-900 dark:text-white">01/07/2024</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-200">Pembayaran Bronze Plan</td>
                            <td class="px-4 py-2 text-blue-600 dark:text-blue-400 font-semibold">Rp 50.000</td>
                            <td class="px-4 py-2"><span class="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded px-2 py-0.5 text-xs font-semibold">Lunas</span></td>
                        </tr>
                        <tr>
                            <td class="px-4 py-2 text-gray-900 dark:text-white">01/06/2024</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-200">Pembayaran Bronze Plan</td>
                            <td class="px-4 py-2 text-blue-600 dark:text-blue-400 font-semibold">Rp 50.000</td>
                            <td class="px-4 py-2"><span class="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded px-2 py-0.5 text-xs font-semibold">Lunas</span></td>
                        </tr>
                        <tr>
                            <td class="px-4 py-2 text-gray-900 dark:text-white">01/05/2024</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-200">Pembayaran Bronze Plan</td>
                            <td class="px-4 py-2 text-blue-600 dark:text-blue-400 font-semibold">Rp 50.000</td>
                            <td class="px-4 py-2"><span class="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded px-2 py-0.5 text-xs font-semibold">Lunas</span></td>
                        </tr>
                        <tr>
                            <td class="px-4 py-2 text-gray-900 dark:text-white">01/04/2024</td>
                            <td class="px-4 py-2 text-gray-700 dark:text-gray-200">Pembayaran Bronze Plan</td>
                            <td class="px-4 py-2 text-blue-600 dark:text-blue-400 font-semibold">Rp 50.000</td>
                            <td class="px-4 py-2"><span class="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded px-2 py-0.5 text-xs font-semibold">Lunas</span></td>
                        </tr>
                    </tbody>
                </table>
                <div class="text-xs text-gray-400 dark:text-gray-500 mt-2">* Hanya menampilkan 4 transaksi terakhir.</div>
            </div>
        </div>
        <button class="w-full md:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-8 rounded-lg font-bold shadow-lg transition-all duration-200 text-base flex items-center justify-center gap-2">
            <i class="fa-solid fa-arrow-up mr-2"></i>
            Upgrade Plan
        </button>
    </div>
</div>

<!-- Live Chat Tab Content -->
<div id="tabContentLiveChat" class="hidden space-y-8">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-100 dark:border-gray-700 p-6 transition-all duration-300 mb-4 flex flex-col items-center justify-center">
        <div class="text-center mb-6">
            <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
            </div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Live Chat Support</h2>
        </div>
        <button id="startLiveChatBtn" class="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-700 text-white py-3 px-6 rounded-xl font-semibold shadow-lg transition-all duration-200 flex items-center gap-3 mx-auto">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
            </svg>
            Mulai Live Chat CS
        </button>
        <p class="mt-4 text-gray-500 dark:text-gray-300 text-center text-sm">Layanan live chat tersedia setiap hari pukul 08.00 - 22.00 WIB.<br>Tim CS kami siap membantu pertanyaan dan kendala Anda.</p>
    </div>
</div>

<!-- Live Chat Bubble Overlay -->
<div id="liveChatOverlay" class="fixed inset-0 z-50 bg-black/30 flex items-end justify-end hidden">
    <!-- Custom Loading Modal -->
    <div id="liveChatLoading" class="absolute inset-0 flex items-center justify-center">
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-blue-200 dark:border-blue-700 px-8 py-8 flex flex-col items-center gap-4 animate__animated animate__fadeInDown">
            <div class="w-14 h-14 flex items-center justify-center mb-2">
                <div class="w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <div class="text-center">
                <div class="text-blue-700 dark:text-blue-200 font-bold text-base mb-1">Kami sedang menghubungkan dengan CS</div>
                <div class="text-gray-500 dark:text-gray-300 text-sm">Harap menunggu ya <span id="liveChatLoadingDots" class="inline-block">...</span></div>
            </div>
        </div>
    </div>
    <div id="liveChatBubble" class="mb-32 mr-6 md:mb-40 md:mr-12 w-80 max-w-[95vw] bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-blue-200 dark:border-blue-700 p-0 flex flex-col animate__animated animate__fadeInUp hidden">
        <div class="flex items-center justify-between px-4 py-3 border-b border-blue-100 dark:border-blue-800 bg-blue-50 dark:bg-blue-950 rounded-t-2xl">
            <div class="flex items-center gap-2">
                <i class="fa-solid fa-headset text-blue-600"></i>
                <span class="font-semibold text-gray-900 dark:text-white">Live Chat CS</span>
            </div>
            <button id="closeLiveChatBubble" class="text-gray-400 hover:text-red-500 transition"><i class="fa-solid fa-times"></i></button>
        </div>
        <div class="flex-1 flex flex-col gap-2 px-4 py-3 bg-white dark:bg-gray-900 overflow-y-auto" style="min-height:120px; max-height:260px;">
            <!-- Connected message -->
            <div id="csConnectedContainer" class="flex items-start gap-2 opacity-0 -translate-x-8 transition-all duration-500">
                <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                    <i class="fa-solid fa-user-check text-green-600"></i>
                </div>
                <div class="bg-green-100 dark:bg-green-800 text-gray-900 dark:text-white rounded-2xl rounded-bl-sm px-4 py-2 text-sm max-w-[70%] shadow">
                    Telah terhubung dengan CS <span id="csRandomName" class="font-semibold"></span>
                </div>
            </div>
            <!-- First CS message -->
            <div id="csMessageContainer" class="flex items-start gap-2 opacity-0 -translate-x-8 transition-all duration-500">
                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <i class="fa-solid fa-headset text-blue-600"></i>
                </div>
                <div class="bg-blue-100 dark:bg-blue-800 text-gray-900 dark:text-white rounded-2xl rounded-bl-sm px-4 py-2 text-sm max-w-[70%] shadow">
                    Halo, ada yang bisa kami bantu?
                </div>
            </div>
        </div>
        <div class="px-4 py-3 border-t border-blue-100 dark:border-blue-800 bg-blue-50 dark:bg-blue-950 rounded-b-2xl flex items-center gap-2">
            <input type="text" class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm" placeholder="Ketik pesan..." disabled>
            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold opacity-60 cursor-not-allowed" disabled>
                <i class="fa-solid fa-paper-plane"></i>
            </button>
        </div>
    </div>
</div>

<style>
.toggle-switch {
  position: relative;
  width: 48px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  transition: background 0.2s;
}
.toggle-track {
  width: 100%;
  height: 14px;
  background: #d1d5db;
  border-radius: 9999px;
  transition: background 0.2s;
  display: block;
}
.toggle-thumb {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 24px;
  height: 24px;
  background: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(59,130,246,0.15);
  transition: transform 0.2s, background 0.2s;
  will-change: transform;
}
.toggle-switch[aria-pressed="true"] .toggle-track {
  background: linear-gradient(90deg, #3b82f6, #6366f1);
}
.toggle-switch[aria-pressed="true"] .toggle-thumb {
  background: #2563eb;
  transform: translateX(20px);
}
.toggle-switch[aria-pressed="false"] .toggle-thumb {
  background: #d1d5db;
  transform: translateX(0);
}
.dark .toggle-switch[aria-pressed="true"] .toggle-track {
  background: linear-gradient(90deg, #60a5fa, #a78bfa);
}
.dark .toggle-switch[aria-pressed="true"] .toggle-thumb {
  background: #60a5fa;
}
.dark .toggle-switch[aria-pressed="false"] .toggle-thumb {
  background: #374151;
}
</style>

<script>
// Profile form AJAX
const profileForm = document.getElementById('profileForm');
const profileToast = document.getElementById('profileToast');
profileForm.addEventListener('submit', function(e) {
    e.preventDefault();
    const data = {
        name: document.getElementById('name').value,
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        bio: document.getElementById('bio').value
    };
    fetch('<?php echo e(route('profile.update')); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
        },
        body: JSON.stringify(data),
    })
    .then(res => res.json())
    .then(data => {
        showProfileToast(data.message || (data.success ? 'Profil berhasil diperbarui' : 'Gagal update profil'), data.success);
    })
    .catch(() => {
        showProfileToast('Gagal update profil', false);
    });
});

// Profile photo upload logic
const changePhotoBtn = document.getElementById('changePhotoBtn');
const profilePhotoInput = document.getElementById('profile-photo-input');
const profilePhotoPreview = document.getElementById('profilePhotoPreview');
const profilePhotoInitial = document.getElementById('profilePhotoInitial');
const photoUploadingSpinner = document.getElementById('photoUploadingSpinner');
const photoProgressBarContainer = document.getElementById('photoProgressBarContainer');
const photoProgressBar = document.getElementById('photoProgressBar');

changePhotoBtn.addEventListener('click', function() {
    profilePhotoInput.click();
});
profilePhotoInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (!file) return;
    // Show spinner and progress
    photoUploadingSpinner.style.display = 'flex';
    photoProgressBarContainer.style.display = 'block';
    photoProgressBar.style.width = '0%';
    // Prepare upload
    const formData = new FormData();
    formData.append('photo', file);
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '<?php echo e(route('profile.upload-photo')); ?>', true);
    xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name=csrf-token]').content);
    xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            photoProgressBar.style.width = percent + '%';
        }
    };
    xhr.onload = () => {
        photoUploadingSpinner.style.display = 'none';
        photoProgressBarContainer.style.display = 'none';
        if (xhr.status === 200) {
            try {
                const data = JSON.parse(xhr.responseText);
                if (data.success) {
                    profilePhotoPreview.src = data.photo_url;
                    profilePhotoPreview.style.display = 'block';
                    profilePhotoInitial.style.display = 'none';
                    showProfileToast('Foto profil berhasil diunggah!', true);
                } else {
                    showProfileToast('Upload gagal', false);
                }
            } catch (e) {
                showProfileToast('Upload gagal: Response bukan JSON', false);
            }
        } else {
            showProfileToast('Upload gagal', false);
        }
    };
    xhr.onerror = () => {
        photoUploadingSpinner.style.display = 'none';
        photoProgressBarContainer.style.display = 'none';
        showProfileToast('Upload gagal', false);
    };
    xhr.send(formData);
});
function showProfileToast(msg, success) {
    profileToast.textContent = msg;
    profileToast.className = 'fixed z-50 right-4 bottom-4 md:right-8 md:bottom-8 px-4 py-3 rounded-lg shadow-lg text-white min-w-[220px] max-w-[90vw] ' + (success ? 'bg-green-600' : 'bg-red-600');
    profileToast.classList.remove('hidden');
    setTimeout(() => profileToast.classList.add('hidden'), 3000);
}

// Tab switching logic
const tabButtons = document.querySelectorAll('[data-tab]');
const tabContents = {
    profile: document.querySelector('.space-y-8'),
    security: document.getElementById('tabContentSecurity'),
    notifications: document.getElementById('tabContentNotifications'),
    billing: document.getElementById('tabContentBilling'),
    livechat: document.getElementById('tabContentLiveChat'),
};
function showTab(tab) {
    Object.keys(tabContents).forEach(key => {
        if (tabContents[key]) tabContents[key].classList.add('hidden');
        tabButtons.forEach(b => b.classList.remove('border-blue-500', 'text-blue-600', 'dark:text-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20', 'active'));
    });
    if (tabContents[tab]) tabContents[tab].classList.remove('hidden');
    // Aktifkan warna icon putih/biru
    const activeBtn = Array.from(tabButtons).find(b => b.dataset.tab === tab);
    if (activeBtn) {
        activeBtn.classList.add('border-blue-500', 'text-blue-600', 'dark:text-blue-400', 'bg-blue-50', 'dark:bg-blue-900/20', 'active');
        activeBtn.querySelector('i').classList.remove('text-white');
        activeBtn.querySelector('i').classList.add('text-blue-600');
    }
    tabButtons.forEach(btn => {
        if (!btn.classList.contains('active')) {
            btn.querySelector('i').classList.remove('text-blue-600');
            btn.querySelector('i').classList.add('text-white');
        }
    });
    // Save to localStorage
    localStorage.setItem('settingsActiveTab', tab);
}
tabButtons.forEach(btn => {
    btn.addEventListener('click', function() {
        showTab(this.dataset.tab);
    });
});
// On page load, restore last tab
const lastTab = localStorage.getItem('settingsActiveTab') || 'profile';
showTab(lastTab);
// Live Chat Bubble logic
const startLiveChatBtn = document.getElementById('startLiveChatBtn');
const liveChatOverlay = document.getElementById('liveChatOverlay');
const liveChatBubble = document.getElementById('liveChatBubble');
const closeLiveChatBubble = document.getElementById('closeLiveChatBubble');
const liveChatLoading = document.getElementById('liveChatLoading');
const csMessageContainer = document.getElementById('csMessageContainer');
const liveChatLoadingDots = document.getElementById('liveChatLoadingDots');
const csConnectedContainer = document.getElementById('csConnectedContainer');
const csRandomName = document.getElementById('csRandomName');
// Animate dots ...
let dotsInterval = null;
function startDotsAnimation() {
    let dots = 0;
    if (liveChatLoadingDots) {
        liveChatLoadingDots.textContent = '';
        dotsInterval = setInterval(() => {
            dots = (dots + 1) % 4;
            liveChatLoadingDots.textContent = '.'.repeat(dots || 3); // always show at least 3 dots
        }, 400);
    }
}
function stopDotsAnimation() {
    if (dotsInterval) clearInterval(dotsInterval);
    if (liveChatLoadingDots) liveChatLoadingDots.textContent = '...';
}
function getRandomCSName() {
    const names = ['Dewi', 'Rizky', 'Andi', 'Sari', 'Budi', 'Putri', 'Agus', 'Lina', 'Fajar', 'Maya', 'Rina', 'Dian', 'Yusuf', 'Nina', 'Rama'];
    return names[Math.floor(Math.random() * names.length)];
}
if (startLiveChatBtn && liveChatOverlay && liveChatBubble && closeLiveChatBubble && liveChatLoading && csMessageContainer && csConnectedContainer && csRandomName) {
    startLiveChatBtn.addEventListener('click', function() {
        liveChatOverlay.classList.remove('hidden');
        liveChatLoading.style.display = 'flex';
        liveChatBubble.classList.add('hidden');
        csMessageContainer.classList.remove('opacity-100', 'translate-x-0');
        csMessageContainer.classList.add('opacity-0', '-translate-x-8');
        csConnectedContainer.classList.remove('opacity-100', 'translate-x-0');
        csConnectedContainer.classList.add('opacity-0', '-translate-x-8');
        startDotsAnimation();
        setTimeout(() => {
            liveChatLoading.style.display = 'none';
            liveChatBubble.classList.remove('hidden');
            stopDotsAnimation();
            // Set random CS name
            csRandomName.textContent = getRandomCSName();
            // Animate connected message in
            setTimeout(() => {
                csConnectedContainer.classList.remove('opacity-0', '-translate-x-8');
                csConnectedContainer.classList.add('opacity-100', 'translate-x-0');
                // After 1.2s, show CS message
                setTimeout(() => {
                    csMessageContainer.classList.remove('opacity-0', '-translate-x-8');
                    csMessageContainer.classList.add('opacity-100', 'translate-x-0');
                }, 1200);
            }, 200); // slight delay for smoothness
        }, 6000); // loading duration 6s
    });
    closeLiveChatBubble.addEventListener('click', function() {
        liveChatOverlay.classList.add('hidden');
        liveChatBubble.classList.add('hidden');
        liveChatLoading.style.display = 'flex';
        csMessageContainer.classList.remove('opacity-100', 'translate-x-0');
        csMessageContainer.classList.add('opacity-0', '-translate-x-8');
        csConnectedContainer.classList.remove('opacity-100', 'translate-x-0');
        csConnectedContainer.classList.add('opacity-0', '-translate-x-8');
        stopDotsAnimation();
    });
    liveChatOverlay.addEventListener('click', function(e) {
        if (e.target === liveChatOverlay) {
            liveChatOverlay.classList.add('hidden');
            liveChatBubble.classList.add('hidden');
            liveChatLoading.style.display = 'flex';
            csMessageContainer.classList.remove('opacity-100', 'translate-x-0');
            csMessageContainer.classList.add('opacity-0', '-translate-x-8');
            csConnectedContainer.classList.remove('opacity-100', 'translate-x-0');
            csConnectedContainer.classList.add('opacity-0', '-translate-x-8');
            stopDotsAnimation();
        }
    });
}
// Change Password Form Logic (dummy, AJAX-ready)
const changePasswordForm = document.getElementById('changePasswordForm');
const changePasswordBtn = document.getElementById('changePasswordBtn');
const changePasswordBtnText = document.getElementById('changePasswordBtnText');
const changePasswordSpinner = document.getElementById('changePasswordSpinner');
const changePasswordFeedback = document.getElementById('changePasswordFeedback');
if (changePasswordForm) {
    changePasswordForm.addEventListener('submit', function(e) {
        e.preventDefault();
        changePasswordBtn.disabled = true;
        changePasswordBtnText.textContent = 'Memproses...';
        changePasswordSpinner.classList.remove('hidden');
        changePasswordFeedback.classList.add('hidden');
        // Dummy delay
        setTimeout(() => {
            const oldPass = document.getElementById('oldPassword').value;
            const newPass = document.getElementById('newPassword').value;
            const confirmPass = document.getElementById('confirmPassword').value;
            if (!oldPass || !newPass || !confirmPass) {
                changePasswordFeedback.textContent = 'Semua field wajib diisi.';
                changePasswordFeedback.className = 'mt-3 text-sm font-semibold text-red-600';
                changePasswordFeedback.classList.remove('hidden');
            } else if (newPass.length < 6) {
                changePasswordFeedback.textContent = 'Password baru minimal 6 karakter.';
                changePasswordFeedback.className = 'mt-3 text-sm font-semibold text-red-600';
                changePasswordFeedback.classList.remove('hidden');
            } else if (newPass !== confirmPass) {
                changePasswordFeedback.textContent = 'Konfirmasi password tidak cocok.';
                changePasswordFeedback.className = 'mt-3 text-sm font-semibold text-red-600';
                changePasswordFeedback.classList.remove('hidden');
            } else {
                changePasswordFeedback.textContent = 'Password berhasil diganti!';
                changePasswordFeedback.className = 'mt-3 text-sm font-semibold text-green-600';
                changePasswordFeedback.classList.remove('hidden');
                changePasswordForm.reset();
            }
            changePasswordBtn.disabled = false;
            changePasswordBtnText.textContent = 'Ganti Password';
            changePasswordSpinner.classList.add('hidden');
        }, 1200);
    });
}
// Dummy 2FA toggle
let twoFAEnabled = false;
const toggle2FA = document.getElementById('toggle2FA');
if (toggle2FA) {
    toggle2FA.addEventListener('click', function() {
        twoFAEnabled = !twoFAEnabled;
        this.className = twoFAEnabled ? 'px-4 py-2 rounded-lg font-semibold transition-all duration-200 text-white bg-green-600 hover:bg-green-700' : 'px-4 py-2 rounded-lg font-semibold transition-all duration-200 text-white bg-gray-400 hover:bg-gray-500';
        this.textContent = twoFAEnabled ? 'Aktif' : 'Nonaktif';
    });
}
// Notification toggles (dummy logic)
function setupNotifToggle(id, initial) {
    const btn = document.getElementById(id);
    if (!btn) return;
    let state = initial;
    btn.setAttribute('aria-pressed', state ? 'true' : 'false');
    btn.addEventListener('click', function() {
        state = !state;
        btn.setAttribute('aria-pressed', state ? 'true' : 'false');
    });
}
setupNotifToggle('notifEmailToggle', true);
setupNotifToggle('notifPushToggle', false);
setupNotifToggle('notifBillingToggle', true);
</script> <?php /**PATH C:\laragon\www\sub4short--plus\resources\views/pages/settings.blade.php ENDPATH**/ ?>