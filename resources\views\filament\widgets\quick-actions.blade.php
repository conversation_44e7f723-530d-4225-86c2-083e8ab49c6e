<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Quick Actions
        </x-slot>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($actions as $action)
                <a href="{{ $action['url'] }}" 
                   class="group relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 hover:shadow-md">
                    
                    <div class="flex items-start space-x-4">
                        <div @class([
                            'flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center transition-colors group-hover:scale-110 duration-200',
                            'bg-primary-100 text-primary-600 dark:bg-primary-900/30 dark:text-primary-400' => $action['color'] === 'primary',
                            'bg-success-100 text-success-600 dark:bg-success-900/30 dark:text-success-400' => $action['color'] === 'success',
                            'bg-warning-100 text-warning-600 dark:bg-warning-900/30 dark:text-warning-400' => $action['color'] === 'warning',
                            'bg-info-100 text-info-600 dark:bg-info-900/30 dark:text-info-400' => $action['color'] === 'info',
                            'bg-danger-100 text-danger-600 dark:bg-danger-900/30 dark:text-danger-400' => $action['color'] === 'danger',
                            'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400' => $action['color'] === 'gray',
                        ])>
                            @switch($action['icon'])
                                @case('heroicon-o-plus-circle')
                                    <x-heroicon-o-plus-circle class="w-6 h-6" />
                                    @break
                                @case('heroicon-o-chart-bar')
                                    <x-heroicon-o-chart-bar class="w-6 h-6" />
                                    @break
                                @case('heroicon-o-users')
                                    <x-heroicon-o-users class="w-6 h-6" />
                                    @break
                                @case('heroicon-o-arrow-down-tray')
                                    <x-heroicon-o-arrow-down-tray class="w-6 h-6" />
                                    @break
                                @case('heroicon-o-cog-6-tooth')
                                    <x-heroicon-o-cog-6-tooth class="w-6 h-6" />
                                    @break
                                @case('heroicon-o-question-mark-circle')
                                    <x-heroicon-o-question-mark-circle class="w-6 h-6" />
                                    @break
                                @default
                                    <x-heroicon-o-squares-2x2 class="w-6 h-6" />
                            @endswitch
                        </div>
                        
                        <div class="flex-1 min-w-0">
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                                {{ $action['title'] }}
                            </h3>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {{ $action['description'] }}
                            </p>
                        </div>
                    </div>
                    
                    <!-- Hover effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-primary-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                </a>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
