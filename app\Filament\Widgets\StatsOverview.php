<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Shortlinks', '2,140')
                ->description('Total links yang dibuat')
                ->descriptionIcon('heroicon-m-link')
                ->color('primary')
                ->chart([7, 2, 10, 3, 15, 4, 17]),

            Stat::make('Total Klik', '15,847')
                ->description('32% increase')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success')
                ->chart([2, 10, 3, 15, 4, 17, 7]),

            Stat::make('Pendapatan Bulan Ini', 'Rp 2,450,000')
                ->description('18% increase')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning')
                ->chart([15, 4, 10, 2, 12, 4, 12]),

            Stat::make('CTR Rata-rata', '3.2%')
                ->description('2% decrease')
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger')
                ->chart([10, 2, 12, 4, 8, 2, 6]),

            Stat::make('User Aktif', '1,247')
                ->description('User yang aktif bulan ini')
                ->descriptionIcon('heroicon-m-users')
                ->color('info')
                ->chart([5, 8, 12, 15, 18, 20, 22]),

            Stat::make('Link Aktif Hari Ini', '89')
                ->description('Links yang diklik hari ini')
                ->descriptionIcon('heroicon-m-cursor-arrow-rays')
                ->color('success')
                ->chart([1, 3, 5, 8, 12, 15, 18]),
        ];
    }
}
