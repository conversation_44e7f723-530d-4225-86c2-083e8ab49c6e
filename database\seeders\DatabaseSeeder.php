<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'username' => 'budisantoso',
            'bio' => 'Digital marketer & penggemar teknologi. Suka ngulik tools baru.',
            'password' => bcrypt('password123'),
            'profile_photo' => null,
        ]);
    }
}
