<!DOCTYPE html>
<html lang="id" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" :class="darkMode ? 'dark' : ''">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Bronze Plan - sub4short+</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .transition-all {
            transition: all 0.3s ease-in-out;
        }
        
        /* Custom Scrollbar Styles */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 10px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2563eb, #7c3aed);
            transform: scale(1.1);
        }
        
        /* Dark mode scrollbar */
        .dark .custom-scrollbar::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #60a5fa, #a78bfa);
        }
        
        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        }
        
        /* Firefox scrollbar */
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #3b82f6 transparent;
        }
        
        .dark .custom-scrollbar {
            scrollbar-color: #60a5fa transparent;
        }
        
        /* Smooth scrolling */
        .custom-scrollbar {
            scroll-behavior: smooth;
        }
        
        /* Hide scrollbar when not needed */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar:horizontal {
            height: 6px;
        }
        
        /* Custom scrollbar for mobile */
        @media (max-width: 768px) {
            .custom-scrollbar::-webkit-scrollbar {
                width: 4px;
            }
            
            .custom-scrollbar::-webkit-scrollbar-thumb {
                background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                border-radius: 8px;
            }
            
            /* Ensure content doesn't overlap with bottom navigation */
            .custom-scrollbar {
                padding-bottom: 80px;
                margin-bottom: 80px;
            }
            
            /* Mobile bottom safe area */
            .mobile-bottom-safe {
                padding-bottom: 100px !important;
                margin-bottom: 20px;
            }
            
            /* Mobile sidebar height adjustment */
            .custom-scrollbar {
                max-height: calc(100vh - 64px - 80px) !important;
            }
        }
        .sidebar-scroll-area {
            max-height: calc(100vh - 64px - 120px); /* 64px header, 120px bottom nav+safe area */
            overflow-y: auto;
            scrollbar-width: thin;
        }
        .sidebar-scroll-area::-webkit-scrollbar {
            width: 6px;
            background: linear-gradient(to bottom, #f3f4f6, #e5e7eb);
        }
        .sidebar-scroll-area::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #cbd5e1, #a1a1aa);
            border-radius: 8px;
        }
        @keyframes spin-pause {
            0% { transform: rotate(0deg); }
            60% { transform: rotate(360deg); }
            70% { transform: rotate(360deg); }
            100% { transform: rotate(720deg); }
        }
        .animate-spin-custom {
            animation: spin-pause 2s linear infinite;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen transition-all duration-300" x-data="{ 
    sidebarOpen: false,
    activeTab: localStorage.getItem('activeTab') || 'dashboard',
    darkMode: localStorage.getItem('darkMode') === 'true',
    notifications: [
        { id: 1, message: 'Shortlink baru berhasil dibuat', time: '2 menit yang lalu', type: 'success' },
        { id: 2, message: 'Pendapatan hari ini: Rp 15.000', time: '1 jam yang lalu', type: 'info' }
    ],
    stats: {
        totalLinks: 24,
        totalClicks: 1250,
        todayClicks: 89,
        totalEarnings: 45000
    },
    linkLimit: {
        current: 24,
        max: 100,
        resetDate: '15 Des 2024',
        plan: 'Bronze'
    },
    showShortlinkForm: false,
    newShortlink: {
        url: '',
        alias: ''
    },
    settingsTab: 'profile',
    showDashboardLoader: true
}" x-init="
    $watch('activeTab', val => localStorage.setItem('activeTab', val));
    $watch('darkMode', val => {
        localStorage.setItem('darkMode', val);
        document.documentElement.classList.toggle('dark', val);
    });
    document.documentElement.classList.toggle('dark', darkMode);
    // Prevent sidebar and shortlink popup open while loading
    sidebarOpen = false;
    showShortlinkForm = false;
    setTimeout(() => { showDashboardLoader = false }, 4000);
">
    <!-- Loading Overlay -->
    <div x-show="showDashboardLoader" x-transition:enter="transition-opacity duration-300" x-transition:leave="transition-opacity duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 z-[9999] flex items-center justify-center" :class="darkMode ? 'bg-blue-900' : 'bg-blue-600'" style="min-height:100vh;">
        <div class="flex flex-col items-center">
            <div id="dashboardLoaderIcon" class="w-20 h-20 flex items-center justify-center">
                <i class="fa-solid fa-link text-white text-5xl animate-spin-custom"></i>
            </div>
            <span class="mt-6 text-white text-lg font-bold tracking-wide">Memuat Dashboard...</span>
        </div>
    </div>
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900 transition-all duration-300">
        <!-- Mobile Backdrop -->
        <div x-show="sidebarOpen" 
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="sidebarOpen = false"
             class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"></div>
        
        <!-- Sidebar -->
        <div :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'" 
             class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 flex flex-col">
            <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-link text-white text-sm"></i>
                    </div>
                    <span class="text-xl font-bold text-gray-900 dark:text-white">sub4short<span class="text-blue-600">+</span></span>
                </div>
                <button @click="sidebarOpen = false" class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fa-solid fa-times"></i>
                </button>
            </div>
            <!-- SCROLLABLE AREA START -->
            <div class="sidebar-scroll-area flex-1 overflow-y-auto custom-scrollbar" style="max-height: calc(100vh - 64px - 100px);">
                <nav class="mt-8 px-4">
                    <div class="space-y-2">
                        <a href="#" @click="activeTab = 'dashboard'; sidebarOpen = false" 
                           :class="activeTab === 'dashboard' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
                           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fa-solid fa-gauge-high w-5 h-5 mr-3"></i>
                            Dashboard
                        </a>
                        <a href="#" @click="activeTab = 'shortlink'; sidebarOpen = false" 
                           :class="activeTab === 'shortlink' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
                           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fa-solid fa-link w-5 h-5 mr-3"></i>
                            Shortlink
                        </a>
                        <a href="#" @click="activeTab = 'analytics'; sidebarOpen = false" 
                           :class="activeTab === 'analytics' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
                           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fa-solid fa-chart-line w-5 h-5 mr-3"></i>
                            Analytics
                            <span class="ml-auto bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs px-2 py-1 rounded-full">Terbatas</span>
                        </a>
                        <a href="#" @click="activeTab = 'withdraw'; sidebarOpen = false" 
                           :class="activeTab === 'withdraw' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
                           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fa-solid fa-wallet w-5 h-5 mr-3"></i>
                            Withdraw
                        </a>
                        <a href="#" @click="activeTab = 'settings'; sidebarOpen = false" 
                           :class="activeTab === 'settings' ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-r-2 border-blue-700' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
                           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200">
                            <i class="fa-solid fa-cog w-5 h-5 mr-3"></i>
                            Pengaturan
                        </a>
                    </div>
                </nav>
                <!-- Limit Link & Upgrade Plan -->
                <div class="mt-8 px-4">
                    <!-- Link Limit Status -->
                    <div class="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl p-4 mb-4 border border-amber-200 dark:border-amber-700">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-link text-amber-600 dark:text-amber-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Limit Link</p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400">Bronze Plan</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">B</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="mb-2">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>Link Terpakai</span>
                                <span x-text="linkLimit.current + ' / ' + linkLimit.max"></span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-to-r from-amber-400 to-yellow-500 h-2 rounded-full transition-all duration-300" 
                                     :style="'width: ' + (linkLimit.current / linkLimit.max * 100) + '%'"></div>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                            <span>
                                <i class="fa-solid fa-calendar mr-1"></i>
                                Reset: <span x-text="linkLimit.resetDate"></span>
                            </span>
                            <span :class="(linkLimit.max - linkLimit.current) <= 10 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'" class="font-medium">
                                <i :class="(linkLimit.max - linkLimit.current) <= 10 ? 'fa-solid fa-exclamation-triangle' : 'fa-solid fa-check-circle'" class="mr-1"></i>
                                <span x-text="linkLimit.max - linkLimit.current"></span> tersisa
                            </span>
                        </div>
                        
                        <!-- Warning when limit almost reached -->
                        <div x-show="(linkLimit.max - linkLimit.current) <= 10" 
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
                            <div class="flex items-center text-xs text-red-700 dark:text-red-300">
                                <i class="fa-solid fa-exclamation-triangle mr-2"></i>
                                <span x-text="'Hanya ' + (linkLimit.max - linkLimit.current) + ' link tersisa! Upgrade untuk limit lebih tinggi.'"></span>
                            </div>
                        </div>
                    </div>
                    

                </div>
            </div>
            <!-- SCROLLABLE AREA END -->
            <!-- Bagian support/help hanya tampil di desktop -->
            <div class="hidden md:block space-y-2 mt-4 px-4 pb-6">
                <div class="px-4 py-2">
                    <p class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Support</p>
                </div>
                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                    <i class="fa-solid fa-question-circle w-5 h-5 mr-3 text-blue-600 dark:text-blue-400"></i>
                    Bantuan
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                    <i class="fa-solid fa-headset w-5 h-5 mr-3 text-green-600 dark:text-green-400"></i>
                    Live Chat
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
                    <i class="fa-solid fa-book w-5 h-5 mr-3 text-purple-600 dark:text-purple-400"></i>
                    Dokumentasi
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 transition-all duration-300 sticky top-0 z-40">
                <div class="flex items-center justify-between h-16 px-6" x-data="navbarProfilePhoto()" x-init="init()">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = true" class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fa-solid fa-bars"></i>
                        </button>
                        <h1 class="ml-4 text-xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Dark Mode Toggle -->
                        <button @click="darkMode = !darkMode" 
                                class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200">
                            <i x-show="!darkMode" class="fa-solid fa-moon text-lg"></i>
                            <i x-show="darkMode" class="fa-solid fa-sun text-lg"></i>
                        </button>
                        
                        <!-- Notifications -->
                        <div class="relative" x-data="{ notifOpen: false, unreadCount: 2 }">
                            <button @click="notifOpen = !notifOpen" class="relative p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-200 group">
                                <i class="fa-regular fa-bell text-lg group-hover:scale-110 transition-transform duration-200"></i>
                                <span x-show="unreadCount > 0" class="absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-red-500 text-white text-xs font-medium rounded-full flex items-center justify-center animate-pulse" x-text="unreadCount"></span>
                            </button>

                            <!-- Dropdown -->
                            <div x-show="notifOpen"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95 translate-y-1"
                                 x-transition:enter-end="opacity-100 scale-100 translate-y-0"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100 translate-y-0"
                                 x-transition:leave-end="opacity-0 scale-95 translate-y-1"
                                 @click.away="notifOpen = false"
                                 class="absolute right-0 mt-3 w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden z-50">

                                <!-- Header -->
                                <div class="px-6 py-4 border-b border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notifikasi</h3>
                                        <div class="flex items-center gap-2">
                                            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-full" x-text="unreadCount + ' baru'"></span>
                                            <button @click="unreadCount = 0" class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium">
                                                Tandai dibaca
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Notifications List -->
                                <div class="max-h-80 overflow-y-auto">
                                    <!-- Notification Item 1 -->
                                    <div class="px-6 py-4 border-b border-gray-50 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 cursor-pointer group">
                                        <div class="flex items-start gap-3">
                                            <div class="flex-shrink-0 mt-1">
                                                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center">
                                                    <i class="fa-solid fa-link text-green-600 dark:text-green-400 text-sm"></i>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center gap-2 mb-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                                        Shortlink baru berhasil dibuat
                                                    </p>
                                                    <div class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                                                </div>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                                                    Link: sub4short.plus/abc123
                                                </p>
                                                <div class="flex items-center gap-2">
                                                    <i class="fa-regular fa-clock text-gray-400 text-xs"></i>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400">2 menit yang lalu</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Notification Item 2 -->
                                    <div class="px-6 py-4 border-b border-gray-50 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 cursor-pointer group">
                                        <div class="flex items-start gap-3">
                                            <div class="flex-shrink-0 mt-1">
                                                <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl flex items-center justify-center">
                                                    <i class="fa-solid fa-coins text-yellow-600 dark:text-yellow-400 text-sm"></i>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center gap-2 mb-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                                        Pendapatan hari ini: Rp 15.000
                                                    </p>
                                                    <div class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                                                </div>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                                                    Total klik: 89 | Konversi: 12%
                                                </p>
                                                <div class="flex items-center gap-2">
                                                    <i class="fa-regular fa-clock text-gray-400 text-xs"></i>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400">1 jam yang lalu</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Notification Item 3 -->
                                    <div class="px-6 py-4 border-b border-gray-50 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200 cursor-pointer group">
                                        <div class="flex items-start gap-3">
                                            <div class="flex-shrink-0 mt-1">
                                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                                                    <i class="fa-solid fa-chart-line text-blue-600 dark:text-blue-400 text-sm"></i>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center gap-2 mb-1">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                                        Analitik mingguan tersedia
                                                    </p>
                                                </div>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                                                    Lihat performa link Anda minggu ini
                                                </p>
                                                <div class="flex items-center gap-2">
                                                    <i class="fa-regular fa-clock text-gray-400 text-xs"></i>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400">3 jam yang lalu</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Footer -->
                                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-750 border-t border-gray-100 dark:border-gray-700">
                                    <button @click="activeTab = 'settings'; notifOpen = false" class="w-full text-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium py-1 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors">
                                        Lihat Semua Notifikasi
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Menu -->
                        <div class="relative" x-data="{ profileOpen: false }">
                            <button @click="profileOpen = !profileOpen" class="flex items-center gap-3 p-2.5 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group">
                                @if(optional(Auth::user())->profile_photo)
                                    <img src="{{ asset(optional(Auth::user())->profile_photo) }}" alt="Foto Profil" class="w-9 h-9 rounded-full object-cover border-2 border-gray-200 dark:border-gray-700 group-hover:border-blue-200 dark:group-hover:border-blue-800 transition-colors">
                                @else
                                    <div class="w-9 h-9 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center shadow-sm">
                                        <span class="text-white text-sm font-semibold">{{ substr(optional(Auth::user())->name ?? 'U', 0, 1) }}</span>
                                    </div>
                                @endif
                                <div class="hidden md:block text-left">
                                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors">
                                        {{ optional(Auth::user())->name ?? 'User' }}
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Bronze Plan</div>
                                </div>
                                <i class="fa-solid fa-chevron-down text-xs text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-all duration-200" :class="profileOpen ? 'rotate-180' : ''"></i>
                            </button>

                            <!-- Dropdown -->
                            <div x-show="profileOpen"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95 translate-y-1"
                                 x-transition:enter-end="opacity-100 scale-100 translate-y-0"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100 translate-y-0"
                                 x-transition:leave-end="opacity-0 scale-95 translate-y-1"
                                 @click.away="profileOpen = false"
                                 class="absolute right-0 mt-3 w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden z-50">

                                <!-- Header -->
                                <div class="px-6 py-5 border-b border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
                                    <div class="flex items-center gap-4">
                                        @if(optional(Auth::user())->profile_photo)
                                            <img src="{{ asset(optional(Auth::user())->profile_photo) }}" alt="Foto Profil" class="w-16 h-16 rounded-xl object-cover border-2 border-white dark:border-gray-600 shadow-sm">
                                        @else
                                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl flex items-center justify-center shadow-sm">
                                                <span class="text-white text-2xl font-bold">{{ substr(optional(Auth::user())->name ?? 'U', 0, 1) }}</span>
                                            </div>
                                        @endif
                                        <div class="flex-1 min-w-0">
                                            <div class="font-bold text-lg text-gray-900 dark:text-white truncate">
                                                {{ optional(Auth::user())->name ?? 'User' }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 truncate">
                                                {{ optional(Auth::user())->email ?? '<EMAIL>' }}
                                            </div>
                                            <div class="text-xs text-blue-600 dark:text-blue-400 font-medium mt-1 flex items-center gap-1">
                                                <span class="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 rounded-full">Bronze</span>
                                                <span class="text-gray-500 dark:text-gray-400">•</span>
                                                <span class="text-gray-500 dark:text-gray-400">@{{ optional(Auth::user())->username ?? 'username' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-2">
                                    <a href="#" @click="activeTab = 'settings'; profileOpen = false" class="flex items-center gap-3 px-6 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                            <i class="fa-solid fa-user text-blue-600 dark:text-blue-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">Profil Saya</div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">Lihat dan edit profil Anda</div>
                                        </div>
                                    </a>
                                    <a href="#" @click="activeTab = 'settings'; profileOpen = false" class="flex items-center gap-3 px-6 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                                            <i class="fa-solid fa-gear text-purple-600 dark:text-purple-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">Pengaturan</div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">Konfigurasi akun Anda</div>
                                        </div>
                                    </a>
                                    <hr class="my-2 border-gray-100 dark:border-gray-700">
                                    <button @click="showLogoutModal = true; profileOpen = false" class="w-full flex items-center gap-3 px-6 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200">
                                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                                            <i class="fa-solid fa-arrow-right-from-bracket text-red-600 dark:text-red-400"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium">Keluar</div>
                                            <div class="text-xs text-red-500 dark:text-red-300">Keluar dari akun Anda</div>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <!-- Logout Modal -->
                            <div id="logoutModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm hidden">
                                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-6 w-11/12 max-w-sm mx-auto flex flex-col items-center">
                                    <div class="w-16 h-16 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
                                        <i class="fa-solid fa-right-from-bracket text-blue-600 dark:text-blue-400 text-3xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 text-center">Apakah Anda yakin ingin keluar?</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-6 text-center">Anda akan keluar dari akun dan kembali ke halaman utama.</p>
                                    <div class="flex w-full space-x-3">
                                        <button id="cancelLogoutBtn" class="flex-1 py-2 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all">Cancel</button>
                                        <button id="confirmLogoutBtn" class="flex-1 py-2 rounded-lg bg-red-600 text-white font-semibold hover:bg-red-700 transition-all flex items-center justify-center">
                                            <span id="logoutText">Yes</span>
                                            <svg id="logoutLoading" class="animate-spin h-5 w-5 text-white ml-2 hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900 transition-all duration-300" style="height:100vh;">
                <div class="container mx-auto px-6 py-8 md:pb-8 pb-24">
                    <!-- Dashboard Content -->
                    <div x-show="activeTab === 'dashboard'">
                        @include('pages.dashboard')
                    </div>
                    
                    <!-- Shortlink Content -->
                    <div x-show="activeTab === 'shortlink'">
                        @include('pages.shortlink')
                    </div>
                    
                    <!-- Analytics Content -->
                    <div x-show="activeTab === 'analytics'">
                        @include('pages.analytics')
                    </div>
                    
                    <!-- Withdraw Content -->
                    <div x-show="activeTab === 'withdraw'">
                        @include('pages.withdraw')
                    </div>
                    
                    <!-- Settings Content -->
                    <div x-show="activeTab === 'settings'">
                        @include('pages.settings')
                    </div>

                    <!-- Payment Process Content -->
                    <div x-show="activeTab === 'payment-process'">
                        @include('pages.process.payment-process')
                    </div>
                    <!-- Pay Content -->
                    <div x-show="activeTab === 'pay'">
                        @include('pages.pay')
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <div class="md:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-50 transition-all duration-300">
        <div class="flex items-center justify-around py-2">
            <button @click="activeTab = 'dashboard'; sidebarOpen = false" 
                    :class="activeTab === 'dashboard' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'"
                    class="flex flex-col items-center p-2 transition-colors duration-200">
                <i class="fa-solid fa-gauge-high text-lg mb-1"></i>
                <span class="text-xs">Dashboard</span>
            </button>
            
            <button @click="activeTab = 'shortlink'; sidebarOpen = false" 
                    :class="activeTab === 'shortlink' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'"
                    class="flex flex-col items-center p-2 transition-colors duration-200">
                <i class="fa-solid fa-list text-lg mb-1"></i>
                <span class="text-xs">Shortlink</span>
            </button>
            
            <!-- Center Button - Create Shortlink -->
            <button @click="showShortlinkForm = true; sidebarOpen = false" 
                    class="flex flex-col items-center p-2 -mt-6">
                <div class="w-12 h-12 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-105">
                    <i class="fa-solid fa-link text-white text-lg"></i>
                </div>
                <span class="text-xs text-blue-600 dark:text-blue-400 mt-1">Short Now</span>
            </button>
            
            <button @click="activeTab = 'analytics'; sidebarOpen = false" 
                    :class="activeTab === 'analytics' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'"
                    class="flex flex-col items-center p-2 transition-colors duration-200">
                <i class="fa-solid fa-chart-line text-lg mb-1"></i>
                <span class="text-xs">Analytics</span>
            </button>
            
            <button @click="activeTab = 'settings'; sidebarOpen = false" 
                    :class="activeTab === 'settings' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'"
                    class="flex flex-col items-center p-2 transition-colors duration-200">
                <i class="fa-solid fa-cog text-lg mb-1"></i>
                <span class="text-xs">Settings</span>
            </button>
        </div>
    </div>

    <!-- Mobile Shortlink Form Modal -->
    <div x-show="showShortlinkForm" 
         x-transition:enter="transition ease-out duration-500"
         x-transition:enter-start="opacity-0 transform translate-y-full scale-95"
         x-transition:enter-end="opacity-100 transform translate-y-0 scale-100"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="opacity-100 transform translate-y-0 scale-100"
         x-transition:leave-end="opacity-0 transform translate-y-full scale-95"
         class="md:hidden fixed inset-0 z-50 flex items-end">
        <!-- Backdrop -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/40 to-transparent backdrop-blur-sm" 
             x-transition:enter="transition ease-out duration-700"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-500"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="showShortlinkForm = false; sidebarOpen = false"></div>
        
        <!-- Form Container -->
        <div class="relative w-full bg-white dark:bg-gray-800 rounded-t-2xl p-6 max-h-[80vh] overflow-y-auto transform transition-all duration-500 shadow-2xl">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Buat Shortlink Baru</h3>
                <button @click="showShortlinkForm = false; sidebarOpen = false" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200">
                    <i class="fa-solid fa-times text-xl"></i>
                </button>
            </div>
            
            <form @submit.prevent="createShortlink()" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">URL Asli</label>
                    <input type="url" x-model="newShortlink.url" placeholder="https://example.com/very-long-url" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Custom Alias (Opsional)</label>
                    <input type="text" x-model="newShortlink.alias" placeholder="my-custom-link" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200">
                </div>
                
                <button type="submit" class="w-full bg-blue-600 dark:bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 active:scale-95">
                    <i class="fa-solid fa-scissors mr-2"></i>
                    Buat Shortlink
                </button>
            </form>
        </div>
    </div>

    <script>
        function createShortlink() {
            // Cek limit link terlebih dahulu
            if (this.linkLimit.current >= this.linkLimit.max) {
                alert('Limit link bulanan Anda telah habis! Silakan upgrade ke plan yang lebih tinggi.');
                return;
            }
            
            // Simulasi pembuatan shortlink
            if (this.newShortlink.url) {
                // Increment link count
                this.linkLimit.current++;
                this.stats.totalLinks++;
                
                // Reset form
                this.newShortlink = {
                    url: '',
                    alias: ''
                };
                this.showShortlinkForm = false;
                this.sidebarOpen = false;
                this.activeTab = 'shortlink';
                
                // Tampilkan notifikasi sukses
                alert('Shortlink berhasil dibuat! Link terpakai: ' + this.linkLimit.current + '/' + this.linkLimit.max);
            } else {
                alert('URL tidak boleh kosong!');
            }
        }
        
        function upgradePlan() {
            // Simulasi upgrade plan
            if (confirm('Apakah Anda yakin ingin upgrade ke Platinum Plan?\n\nFitur yang akan didapat:\n• Analytics lengkap\n• Custom domain\n• Priority support\n• Dan banyak lagi!')) {
                // Redirect ke halaman upgrade atau payment
                alert('Anda akan diarahkan ke halaman pembayaran...');
                // window.location.href = '/upgrade-plan';
            }
        }

        document.addEventListener('alpine:init', () => {
            Alpine.data('navbarProfilePhoto', () => ({
                open: false,
                showLogoutModal: false,
                loadingLogout: false,
                photoUrl: '{{ optional(Auth::user())->profile_photo ? asset(optional(Auth::user())->profile_photo) : '' }}',
                init() {
                    window.addEventListener('profile-photo-updated', (e) => {
                        this.photoUrl = e.detail.url;
                    });
                },
                logout() {
                    this.loadingLogout = true;
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                }
            }));
        });

        // Dropdown logic
        const profileDropdownBtn = document.getElementById('profileDropdownBtn');
        const profileDropdown = document.getElementById('profileDropdown');
        document.addEventListener('click', function(e) {
            if (profileDropdownBtn.contains(e.target)) {
                profileDropdown.classList.toggle('hidden');
            } else if (!profileDropdown.contains(e.target)) {
                profileDropdown.classList.add('hidden');
            }
        });
        // Logout modal logic
        const logoutBtn = document.getElementById('logoutBtn');
        const logoutModal = document.getElementById('logoutModal');
        const cancelLogoutBtn = document.getElementById('cancelLogoutBtn');
        const confirmLogoutBtn = document.getElementById('confirmLogoutBtn');
        const logoutText = document.getElementById('logoutText');
        const logoutLoading = document.getElementById('logoutLoading');
        logoutBtn.addEventListener('click', function() {
            profileDropdown.classList.add('hidden');
            logoutModal.classList.remove('hidden');
        });
        cancelLogoutBtn.addEventListener('click', function() {
            logoutModal.classList.add('hidden');
            logoutText.classList.remove('hidden');
            logoutLoading.classList.add('hidden');
            confirmLogoutBtn.disabled = false;
        });
        confirmLogoutBtn.addEventListener('click', function() {
            confirmLogoutBtn.disabled = true;
            logoutText.classList.add('hidden');
            logoutLoading.classList.remove('hidden');
            setTimeout(function() {
                window.location.href = '/';
            }, 1500);
        });



        // Prevent sidebar and shortlink popup open while loading
        function preventOpenWhileLoading() {
            document.querySelectorAll('[data-sidebar-trigger], [data-shortlink-trigger]').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (window.Alpine && (Alpine.store('showDashboardLoader') || document.querySelector('[x-data]')?.__x?.getUnobservedProperty('showDashboardLoader'))) {
                        e.stopImmediatePropagation();
                        e.preventDefault();
                    }
                }, true);
            });
        }
        document.addEventListener('DOMContentLoaded', preventOpenWhileLoading);
    </script>
</body>
</html> 