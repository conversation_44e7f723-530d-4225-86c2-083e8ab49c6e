<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Recent Activities
        </x-slot>

        <div class="space-y-4">
            @foreach($activities as $activity)
                <div class="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                    <div class="flex-shrink-0">
                        <div @class([
                            'w-8 h-8 rounded-full flex items-center justify-center',
                            'bg-success-100 text-success-600 dark:bg-success-900/30 dark:text-success-400' => $activity['color'] === 'success',
                            'bg-primary-100 text-primary-600 dark:bg-primary-900/30 dark:text-primary-400' => $activity['color'] === 'primary',
                            'bg-warning-100 text-warning-600 dark:bg-warning-900/30 dark:text-warning-400' => $activity['color'] === 'warning',
                            'bg-danger-100 text-danger-600 dark:bg-danger-900/30 dark:text-danger-400' => $activity['color'] === 'danger',
                            'bg-info-100 text-info-600 dark:bg-info-900/30 dark:text-info-400' => $activity['color'] === 'info',
                        ])>
                            @switch($activity['type'])
                                @case('link_created')
                                    <x-heroicon-o-link class="w-4 h-4" />
                                    @break
                                @case('click')
                                    <x-heroicon-o-cursor-arrow-rays class="w-4 h-4" />
                                    @break
                                @case('revenue')
                                    <x-heroicon-o-banknotes class="w-4 h-4" />
                                    @break
                                @case('user_registered')
                                    <x-heroicon-o-user-plus class="w-4 h-4" />
                                    @break
                                @case('link_expired')
                                    <x-heroicon-o-clock class="w-4 h-4" />
                                    @break
                                @case('high_traffic')
                                    <x-heroicon-o-arrow-trending-up class="w-4 h-4" />
                                    @break
                                @default
                                    <x-heroicon-o-bell class="w-4 h-4" />
                            @endswitch
                        </div>
                    </div>
                    
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {{ $activity['title'] }}
                        </p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {{ $activity['description'] }}
                        </p>
                        <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            {{ $activity['time'] }}
                        </p>
                    </div>
                </div>
            @endforeach
        </div>
        
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <a href="#" class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
                View all activities →
            </a>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
