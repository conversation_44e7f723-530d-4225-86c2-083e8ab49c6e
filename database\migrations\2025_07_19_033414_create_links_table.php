<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('links', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('original_url', 2048);
            $table->string('short_code', 50)->unique();
            $table->string('custom_alias', 100)->nullable();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->integer('clicks')->default(0);
            $table->integer('today_clicks')->default(0);
            $table->decimal('earnings', 10, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_clicked_at')->nullable();
            $table->json('click_data')->nullable(); // Store click analytics
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['short_code']);
            $table->index(['user_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('links');
    }
};
