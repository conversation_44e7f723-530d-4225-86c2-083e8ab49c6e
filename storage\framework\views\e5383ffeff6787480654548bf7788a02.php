<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30">
                <i class="fa-solid fa-link text-blue-600 dark:text-blue-400"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Links</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white" x-text="stats.totalLinks || 0"></p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30">
                <i class="fa-solid fa-mouse-pointer text-green-600 dark:text-green-400"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Klik</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white" x-text="stats.totalClicks || 0"></p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30">
                <i class="fa-solid fa-chart-line text-yellow-600 dark:text-yellow-400"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Klik Hari Ini</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white" x-text="stats.todayClicks || 0"></p>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30">
                <i class="fa-solid fa-money-bill-wave text-purple-600 dark:text-purple-400"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Pendapatan</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">Rp <span x-text="(stats.totalEarnings || 0).toLocaleString()"></span></p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-all duration-300">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Aksi Cepat</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button @click="activeTab = 'shortlink'; sidebarOpen = false" class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200">
            <div class="text-center">
                <i class="fa-solid fa-plus text-2xl text-gray-400 dark:text-gray-500 mb-2"></i>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Buat Shortlink Baru</p>
            </div>
        </button>
        <button @click="activeTab = 'analytics'; sidebarOpen = false" class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-green-500 dark:hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200">
            <div class="text-center">
                <i class="fa-solid fa-chart-bar text-2xl text-gray-400 dark:text-gray-500 mb-2"></i>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Lihat Statistik</p>
            </div>
        </button>
        <button @click="activeTab = 'withdraw'; sidebarOpen = false" class="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-purple-500 dark:hover:border-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-200">
            <div class="text-center">
                <i class="fa-solid fa-wallet text-2xl text-gray-400 dark:text-gray-500 mb-2"></i>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Tarik Dana</p>
            </div>
        </button>
    </div>
</div>

<!-- Recent Activity -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300">
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Aktivitas Terbaru</h2>
        <button onclick="refreshActivity()" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium flex items-center gap-1">
            <i class="fa-solid fa-refresh text-xs"></i>
            Refresh
        </button>
    </div>

    <!-- Loading State -->
    <div x-show="loadingActivities" class="space-y-4">
        <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg animate-pulse">
            <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div class="ml-4 flex-1">
                <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
            </div>
            <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
        </div>
        <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg animate-pulse">
            <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div class="ml-4 flex-1">
                <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-2"></div>
                <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
            </div>
            <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
        </div>
        <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg animate-pulse">
            <div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div class="ml-4 flex-1">
                <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-4/5 mb-2"></div>
                <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/5"></div>
            </div>
            <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-12"></div>
        </div>
    </div>

    <!-- Activities List -->
    <div x-show="!loadingActivities && recentActivities.length > 0" class="space-y-4">
        <template x-for="activity in recentActivities" :key="activity.id">
            <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                <div class="w-10 h-10 rounded-full flex items-center justify-center" :class="activity.iconBg">
                    <i :class="'fa-solid ' + activity.icon + ' ' + activity.iconColor"></i>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-900 dark:text-white" x-text="activity.title"></p>
                    <p class="text-xs text-gray-500 dark:text-gray-400" x-text="activity.description"></p>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400" x-text="activity.time_ago"></span>
            </div>
        </template>
    </div>

    <!-- Empty State -->
    <div x-show="!loadingActivities && recentActivities.length === 0" class="text-center py-8" style="display: none;">
        <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fa-solid fa-clock text-gray-400 dark:text-gray-500 text-xl"></i>
        </div>
        <p class="text-gray-500 dark:text-gray-400 text-sm">Belum ada aktivitas terbaru</p>
        <p class="text-gray-400 dark:text-gray-500 text-xs mt-1">Aktivitas akan muncul setelah Anda membuat shortlink atau mendapat klik</p>
    </div>
</div>

<script>
    // Refresh activity function
    async function refreshActivity() {
        try {
            const response = await fetch('/api/dashboard/activity', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            if (response.ok) {
                const data = await response.json();
                const dashboardData = Alpine.$data(document.body);
                if (dashboardData) {
                    dashboardData.recentActivities = data.activities || [];
                }
            }
        } catch (error) {
            console.error('Error refreshing activity:', error);
        }
    }
</script><?php /**PATH C:\laragon\www\sub4short--plus\resources\views/pages/dashboard.blade.php ENDPATH**/ ?>