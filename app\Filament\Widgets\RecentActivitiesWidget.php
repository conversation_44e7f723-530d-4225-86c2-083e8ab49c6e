<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;

class RecentActivitiesWidget extends Widget
{
    protected static string $view = 'filament.widgets.recent-activities';
    
    protected static ?int $sort = 4;
    
    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];

    protected function getViewData(): array
    {
        return [
            'activities' => [
                [
                    'type' => 'link_created',
                    'icon' => 'heroicon-o-link',
                    'color' => 'success',
                    'title' => 'New shortlink created',
                    'description' => 'sub4short.plus/new123',
                    'time' => '2 minutes ago',
                ],
                [
                    'type' => 'click',
                    'icon' => 'heroicon-o-cursor-arrow-rays',
                    'color' => 'primary',
                    'title' => 'Link clicked',
                    'description' => 'sub4short.plus/abc123 - 15 clicks',
                    'time' => '5 minutes ago',
                ],
                [
                    'type' => 'revenue',
                    'icon' => 'heroicon-o-banknotes',
                    'color' => 'warning',
                    'title' => 'Revenue earned',
                    'description' => 'Rp 25,000 from clicks',
                    'time' => '10 minutes ago',
                ],
                [
                    'type' => 'user_registered',
                    'icon' => 'heroicon-o-user-plus',
                    'color' => 'info',
                    'title' => 'New user registered',
                    'description' => '<EMAIL>',
                    'time' => '15 minutes ago',
                ],
                [
                    'type' => 'link_expired',
                    'icon' => 'heroicon-o-clock',
                    'color' => 'danger',
                    'title' => 'Link expired',
                    'description' => 'sub4short.plus/old456',
                    'time' => '1 hour ago',
                ],
                [
                    'type' => 'high_traffic',
                    'icon' => 'heroicon-o-arrow-trending-up',
                    'color' => 'success',
                    'title' => 'High traffic detected',
                    'description' => 'sub4short.plus/viral789 - 500+ clicks',
                    'time' => '2 hours ago',
                ],
            ]
        ];
    }
}
